.dashboard-container {
  height: 100vh; /* 使容器高度占满视口 */
  display: flex; /* 启用 Flexbox 布局 */
  flex-direction: column; /* 垂直方向排列子元素 */
}

.header {
  background-color: #242f42; /* 导航栏背景色 */
  color: #fff; /* 文字颜色 */
  display: flex; /* 启用 Flexbox 布局 */
  justify-content: space-between; /* 子元素两端对齐 */
  align-items: center; /* 垂直居中对齐 */
  padding: 0 20px; /* 内边距 */
  height: 60px; /* 固定高度 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 导航栏阴影 */

  .logo {
    display: flex; /* 启用 Flexbox 布局 */
    align-items: center; /* 垂直居中对齐 */

    .logo-img {
      height: 32px; /* Logo 图片高度 */
      margin-right: 10px; /* Logo 图片右侧边距 */
    }

    span {
      font-size: 20px; /* 系统名称字体大小 */
      font-weight: bold; /* 字体加粗 */
    }
  }

  .user-menu {
    color: #fff; /* 用户菜单文字颜色 */
    cursor: pointer; /* 鼠标指针样式 */

    .el-dropdown-link {
      display: flex; /* 启用 Flexbox 布局 */
      align-items: center; /* 垂直居中对齐 */
      .el-icon-user {
        font-size: 20px; /* 用户图标字体大小 */
        margin-right: 8px; /* 用户图标右侧边距 */
      }
    }
    /* 消息通知徽标样式 */
    .notification-badge {
      .el-icon-bell {
        font-size: 18px; /* 铃铛图标字体大小 */
      }
      .el-badge__content {
        background-color: #F56C6C; /* 徽标背景色 */
        border-radius: 10px; /* 徽标圆角 */
        padding: 2px 6px; /* 徽标内边距 */
        font-size: 12px; /* 徽标字体大小 */
        line-height: 1; /* 徽标行高 */
        white-space: nowrap; /* 徽标不换行 */
        top: 0px; /* 徽标定位 */
        right: 0px;
        transform: translateY(-50%) translateX(100%);
      }
    }
  }
}

.main-content {
  flex: 1; /* 主内容区占据剩余空间 */
  padding: 20px; /* 内边距 */
  background-color: #f0f2f5; /* 背景色 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.upload-card {
  margin-bottom: 20px; /* 上传卡片底部外边距 */
  background-color: #f5f7fa; /* 上传区背景色 */
  border: 1px dashed #d9d9d9; /* 虚线边框 */
  border-radius: 6px; /* 圆角 */

  .el-upload-dragger {
    background-color: #f5f7fa; /* 拖拽区域背景色 */
    border: none; /* 移除默认边框 */
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 悬停阴影 */
    transition: box-shadow 0.3s ease; /* 阴影过渡动画 */
  }

  .el-icon-upload {
    font-size: 67px; /* 上传图标字体大小 */
    color: #c0c4cc; /* 上传图标颜色 */
    margin: 40px 0 16px; /* 上传图标外边距 */
    line-height: 50px; /* 上传图标行高 */
  }

  .el-upload__text {
    color: #606266; /* 文字颜色 */
    em {
      color: #409EFF; /* 强调文字颜色 */
      font-style: normal; /* 移除斜体 */
    }
  }

  .el-upload__tip {
    margin-top: 10px; /* 提示文字上外边距 */
    font-size: 12px; /* 提示文字字体大小 */
    color: #909399; /* 提示文字颜色 */
  }
}

.analysis-cards {
  margin-top: 30px; /* 上外边距 */
  h3 {
    margin-bottom: 20px; /* 标题底部外边距 */
    color: #303133; /* 标题颜色 */
  }

  .analysis-mode-card {
    margin-bottom: 20px; /* 卡片底部外边距 */
    cursor: pointer; /* 鼠标指针样式 */
    background-color: #ffffff; /* 功能卡片背景色 */
    transition: all 0.3s ease; /* 过渡动画 */

    &:hover {
      transform: translateY(-5px); /* 悬停上移 */
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); /* 悬停阴影 */
      border-color: #409EFF; /* 悬停边框高亮 */
    }

    &.is-selected {
      border: 2px solid #409EFF; /* 选中状态边框 */
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2); /* 选中状态阴影 */
    }

    .el-card__body {
      display: flex; /* 启用 Flexbox 布局 */
      flex-direction: column; /* 垂直方向排列子元素 */
      align-items: center; /* 水平居中对齐 */
      text-align: center; /* 文字居中 */
      padding: 30px 20px; /* 内边距 */
    }

    .mode-icon {
      font-size: 48px; /* 图标字体大小 */
      color: #409EFF; /* 图标颜色 */
      margin-bottom: 15px; /* 图标底部外边距 */
    }

    .mode-name {
      font-size: 18px; /* 模式名称字体大小 */
      font-weight: bold; /* 字体加粗 */
      color: #303133; /* 文字颜色 */
      margin-bottom: 8px; /* 底部外边距 */
    }

    .mode-description {
      font-size: 14px; /* 描述字体大小 */
      color: #909399; /* 描述颜色 */
    }
  }
}

.dashboard-widgets {
  margin-top: 20px; /* 上外边距 */
}

.recent-files-card, .data-overview-card {
  margin-bottom: 20px; /* 卡片底部外边距 */
  .el-card__header {
    padding: 18px 20px; /* 头部内边距 */
    border-bottom: 1px solid #EBEEF5; /* 底部边框 */
    box-sizing: border-box; /* 盒模型 */
  }
}

.el-timeline {
  padding-left: 0; /* 移除左侧内边距 */
}

.el-timeline-item {
  padding-bottom: 20px; /* 底部内边距 */

  .el-card {
    margin-bottom: 0; /* 移除卡片底部外边距 */
    h4 {
      margin-top: 0; /* 移除标题上外边距 */
      margin-bottom: 10px; /* 标题底部外边距 */
      color: #303133; /* 标题颜色 */
    }
    p {
      font-size: 14px; /* 文字字体大小 */
      color: #606266; /* 文字颜色 */
      display: flex; /* 启用 Flexbox 布局 */
      align-items: center; /* 垂直居中对齐 */
      .el-tag {
        margin-right: 10px; /* 标签右侧外边距 */
      }
      .el-progress {
        flex: 1; /* 进度条占据剩余空间 */
        margin-left: 10px; /* 进度条左侧外边距 */
      }
    }
  }
}

.stats-row {
  margin-bottom: 20px; /* 统计行底部外边距 */
}

.stat-card {
  .el-card__body {
    text-align: center; /* 文字居中 */
    padding: 20px; /* 内边距 */
  }
  .stat-title {
    font-size: 14px; /* 标题字体大小 */
    color: #909399; /* 标题颜色 */
    margin-bottom: 8px; /* 底部外边距 */
  }
  .stat-value {
    font-size: 28px; /* 值字体大小 */
    font-weight: bold; /* 字体加粗 */
    color: #303133; /* 值颜色 */
    margin-bottom: 5px; /* 底部外边距 */
  }
  .stat-trend {
    font-size: 12px; /* 趋势文字字体大小 */
    display: flex; /* 启用 Flexbox 布局 */
    align-items: center; /* 垂直居中对齐 */
    justify-content: center; /* 水平居中对齐 */

    &.up {
      color: #67C23A; /* 上升趋势颜色 */
    }
    &.down {
      color: #F56C6C; /* 下降趋势颜色 */
    }
    i {
      margin-right: 4px; /* 图标右侧外边距 */
    }
  }
}

.chart-container {
  min-height: 300px; /* 图表容器最小高度 */
  display: flex; /* 启用 Flexbox 布局 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header {
    padding: 0 10px; /* 小屏幕导航栏内边距 */
    .logo span {
      font-size: 16px; /* 小屏幕系统名称字体大小 */
    }
  }
  .main-content {
    padding: 10px; /* 小屏幕主内容区内边距 */
  }
  .analysis-cards {
    .el-col {
      max-width: 100%; /* 小屏幕下卡片宽度占满 */
      flex: 0 0 100%; /* 小屏幕下卡片宽度占满 */
    }
    .analysis-mode-card {
      .el-card__body {
        padding: 20px; /* 小屏幕卡片内边距 */
      }
    }
  }
  .dashboard-widgets {
    .el-col-12 {
      width: 100%; /* 小屏幕下卡片宽度占满 */
    }
  }
  .stats-row .el-col-8 {
    width: 100%; /* 小屏幕下统计卡片宽度占满 */
    margin-bottom: 10px; /* 底部外边距 */
  }
}
