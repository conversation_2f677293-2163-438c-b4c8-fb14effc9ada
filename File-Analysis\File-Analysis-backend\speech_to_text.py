import requests
import wave
import vosk
import json
import time
import os
import traceback
import shutil
from config import LLM_CONFIG, REPORTS_CONFIG

def remove_duplicate_words(text):
    """移除文本中的重复词汇"""
    result = ""
    current_word = ""
    for char in text:
        if char.isalpha() or char.isdigit():
            current_word += char
        else:
            if current_word and current_word not in result:
                result += current_word
            current_word = ""
            result += char
    if current_word and current_word not in result:
        result += current_word
    return result

def yu_to_text(wav):
    """
    将WAV音频文件转换为文本
    
    Args:
        wav (str): WAV文件路径
        
    Returns:
        str: 转换后的文本或错误信息
    """
    try:
        print(f"开始处理WAV文件: {wav}")
        
        # 检查文件是否存在
        if not os.path.exists(wav):
            print(f"错误: 文件不存在 {wav}")
            return f"错误: 文件不存在 {wav}"
            
        # 创建临时WAV文件（如果原文件没有.wav扩展名）
        temp_wav = None
        if not wav.lower().endswith('.wav'):
            temp_wav = f"{wav}.wav"
            try:
                # 如果临时文件已存在，说明之前已经处理过
                if not os.path.exists(temp_wav):
                    shutil.copy2(wav, temp_wav)
                    print(f"创建临时WAV文件: {temp_wav}")
                else:
                    print(f"临时WAV文件已存在: {temp_wav}")
                wav = temp_wav
            except Exception as e:
                print(f"创建临时WAV文件失败: {str(e)}")
                traceback.print_exc()
                return f"创建临时WAV文件失败: {str(e)}"
        
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 使用相对路径加载vosk模型
        model_path = os.path.join(current_dir, "model", "vosk-model-small-cn-0.22")
        if not os.path.exists(model_path):
            print(f"错误: Vosk模型不存在于 {model_path}")
            return f"错误: Vosk模型不存在于 {model_path}"
            
        print(f"加载Vosk模型: {model_path}")
        try:
            model = vosk.Model(model_path)
            print("Vosk模型加载成功")
        except Exception as e:
            print(f"加载Vosk模型失败: {str(e)}")
            traceback.print_exc()
            return f"加载Vosk模型失败: {str(e)}"

        # 打开WAV文件
        try:
            print(f"打开WAV文件进行识别: {wav}")
            wf = wave.open(wav, "rb")
            rec = vosk.KaldiRecognizer(model, wf.getframerate())
            print(f"WAV文件已打开: 通道数={wf.getnchannels()}, 采样率={wf.getframerate()}")
        except Exception as e:
            print(f"打开WAV文件进行识别失败: {str(e)}")
            traceback.print_exc()
            return f"打开WAV文件进行识别失败: {str(e)}"

        # 逐帧读取WAV文件
        print("开始逐帧读取WAV文件进行识别...")
        text_result = []
        try:
            while True:
                data = wf.readframes(4000)
                if len(data) == 0:
                    break
                if rec.AcceptWaveform(data):
                    result = json.loads(rec.Result())
                    if "text" in result and result["text"]:
                        text_result.append(result["text"])
                        print(f"识别片段: {result['text']}")
            
            # 处理最后的结果
            final_result = json.loads(rec.FinalResult())
            if "text" in final_result and final_result["text"]:
                text_result.append(final_result["text"])
                print(f"最终片段: {final_result['text']}")
                
        except Exception as e:
            print(f"WAV文件识别过程中出错: {str(e)}")
            traceback.print_exc()
            return f"WAV文件识别过程中出错: {str(e)}"

        # 输出最终结果
        print("处理识别结果...")
        final_text = " ".join(text_result)
        if not final_text:
            print("警告: 没有识别出任何文本")
            
        finally_text = final_text.replace(' ','')
        finally_text1 = finally_text.replace('允儿','圆')
        finally_text2 = finally_text1.replace('伞','3')
        print(f"处理后的识别结果: {finally_text2}")
        
        # 使用自定义函数处理文本
        result_text = remove_duplicate_words(finally_text2)
        print(f"最终处理结果: {result_text}")
        
        # 返回最终结果
        return result_text
        
    except Exception as e:
        print(f"语音转文本过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()
        return f"语音转文本过程中发生未捕获的异常: {str(e)}"

def analyze_emotion_with_llm(text_content):
    """
    使用大模型对文本进行情感分析
    
    Args:
        text_content (str): 需要分析的文本内容
    
    Returns:
        str: 情感分析结果
    """
    try:
        print(f"开始进行情感分析，文本长度: {len(text_content)} 字符")
        
        # 使用配置文件中的提示词模板路径
        fx_prompt_path = LLM_CONFIG['PROMPT_FILES']['EMOTION_ANALYSIS']
        
        with open(fx_prompt_path, 'r', encoding='utf-8') as file:
            fx_prompt = file.read()
        
        # 构建完整提示词，添加格式化说明
        prompt = f"{fx_prompt}\n\n文本内容：\n{text_content}\n\n注意：请使用标准Markdown格式进行排版，使用#、##等作为标题标记，避免使用星号(*)作为标记。"
        
        # 调用大模型API，使用配置信息
        url = f"{LLM_CONFIG['API_URL']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {LLM_CONFIG['API_KEY']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": LLM_CONFIG['MODEL_NAME'],
            "messages": [
                {"role": "system", "content": "你是一个专业的心理咨询师，擅长分析语音文本中的情感和心理状态。请使用标准Markdown格式输出分析结果，使用#、##等作为标题标记。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.5
        }
        
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            analysis_result = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"情感分析完成，返回结果长度: {len(analysis_result)} 字符")
            return analysis_result
        else:
            error_msg = f"情感分析API调用失败，状态码: {response.status_code}, 错误信息: {response.text}"
            print(error_msg)
            return f"情感分析失败: {error_msg}"
            
    except Exception as e:
        error_msg = f"情感分析过程中发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return error_msg

def summarize_analysis_with_llm(analysis_result):
    """
    使用大模型对情感分析结果进行二次总结
    
    Args:
        analysis_result (str): 情感分析结果
        
    Returns:
        str: 总结后的结果
    """
    try:
        print("开始对分析结果进行总结")
        
        # 使用配置文件中的提示词模板路径
        zj_prompt_path = LLM_CONFIG['PROMPT_FILES']['SUMMARY']
        
        with open(zj_prompt_path, 'r', encoding='utf-8') as file:
            zj_prompt = file.read()
        
        # 构建完整提示词，添加格式化说明
        prompt = f"""请严格按照以下格式对分析结果进行总结：

# 情感分析报告

## 分析过程

按照情感分析的过程进行总结
1. 情绪状态识别：识别文本中表达的主要情绪（如快乐、悲伤、愤怒、恐惧、焦虑等）
2. 情感强度评估：评估情绪的强度级别（1-5级，1为轻微，5为极强）
3. 潜在心理需求：分析说话者可能的潜在心理需求
4. 语言特征分析：分析语言表达方式中的特殊模式（重复、停顿、词汇选择等）
5. 认知倾向：分析文本中反映的思维模式和认知偏差
6. 人际关系线索：分析文本中提及的人际关系情况
7. 压力源识别：识别可能的压力来源和应对方式
8. 情感变化：分析文本中情感的变化趋势
注：对所有分析维度，如无明显线索，请标注"此维度无明显线索"

## 结论

核心结论：

经分析，当前主体情感具有（必须在此处填写3-5个具体情感，从下面列表中选择）的心理特征。
建议关注（）方面，可考虑（）等干预方式。

可选择的情感状态：
积极、希望、乐观、信心、感恩、消极、悲观、绝望、抑郁、焦虑、恐惧、恐慌、愤怒、敌视、报复心理、强迫观念、疑病、自暴自弃、自满、自负、
自卑、偏执、孤独、不合群、依赖、从众心理、猜忌、信任、嫉妒、敏感、空虚、压抑、逆反、逆来顺受、懒散、怀旧、挑战、后悔、愧疚、麻木、逃避现实、报复

分析结果：
{analysis_result}

请注意：
1. 严格按照上述格式生成报告
2. 请使用Markdown语法，使用#、##等作为标题标记
3. 在结论部分必须列出3-5个情感状态，不能少于3个，形如"经分析，当前主体情感具有（中性、平静、理性）的心理特征"
4. 确保每个分析维度都有内容，如无明显线索则标注"此维度无明显线索"
5. 不要添加任何未在模板中指定的标题或部分
"""
        
        # 调用大模型API，使用配置信息
        url = f"{LLM_CONFIG['API_URL']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {LLM_CONFIG['API_KEY']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": LLM_CONFIG['MODEL_NAME'],
            "messages": [
                {"role": "system", "content": "你是一个专业的心理咨询师，擅长分析语音文本中的情感和心理状态。请严格按照指定格式输出分析报告，不要添加、修改或删除模板中的任何标题和结构。在结论部分必须列出3-5个情感状态，不能少于3个。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3
        }
        
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            summary_result = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"分析总结完成，返回结果长度: {len(summary_result)} 字符")
            return summary_result
        else:
            error_msg = f"总结API调用失败，状态码: {response.status_code}, 错误信息: {response.text}"
            print(error_msg)
            return f"总结失败: {error_msg}"
            
    except Exception as e:
        error_msg = f"总结过程中发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return error_msg

def convert_analysis_to_word(text_content, analysis_result, summary_result, file_name_base):
    """
    将分析结果转换为Word文档
    
    Args:
        text_content (str): 原始文本内容
        analysis_result (str): 情感分析结果
        summary_result (str): 总结结果
        file_name_base (str): 输出文件名基础（不含扩展名）
    
    Returns:
        tuple: (成功标志, 文件路径或错误消息)
    """
    try:
        import os
        from config import REPORTS_CONFIG
        
        print(f"开始将分析结果转换为Word文档: {file_name_base}")
        
        # 确保报告目录存在
        report_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        os.makedirs(report_dir, exist_ok=True)
        print(f"报告保存路径: {report_dir}")
        
        # 获取不带路径和扩展名的文件名（仅文件名部分）
        base_filename = os.path.splitext(os.path.basename(file_name_base))[0]
        
        # 构建临时Markdown文件路径
        temp_md_path = os.path.join(report_dir, f"{base_filename}_analysis.md")
        
        # 组合完整的Markdown内容，包含文本内容、情感分析和总结
        full_content = f"# 语音文本分析报告\n\n## 原始文本内容\n\n{text_content}\n\n## 情感分析\n\n{analysis_result}\n\n## 总结\n\n{summary_result}"
        
        # 写入Markdown内容到临时文件
        with open(temp_md_path, 'w', encoding='utf-8') as f:
            f.write(full_content)
            
        # 直接使用convert_with_spire函数处理转换
        from markdown import MarkdownConverter
        
        converter = MarkdownConverter()
        output_file = os.path.join(report_dir, f"{base_filename}_analysis.docx")
        
        success, message = converter.convert_with_spire(temp_md_path, output_file)
        
        # 删除临时Markdown文件
        if os.path.exists(temp_md_path):
            os.remove(temp_md_path)
            
        if success:
            # 清除评估警告
            converter.remove_evaluation_warning(output_file)
            # 应用格式设置
            converter.format_word_document(output_file)
            return True, output_file
        else:
            return False, message
            
    except Exception as e:
        error_msg = f"将分析结果转换为Word文档时发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, error_msg

def save_analysis_to_markdown(text_content, analysis_result, summary_result, file_name_base):
    """
    将分析结果保存为Markdown文件
    
    Args:
        text_content (str): 原始文本内容
        analysis_result (str): 情感分析结果
        summary_result (str): 总结结果
        file_name_base (str): 输出文件名基础（不含扩展名）
    
    Returns:
        tuple: (成功标志, 文件路径或错误消息)
    """
    try:
        import os
        from config import REPORTS_CONFIG
        
        print(f"开始将分析结果保存为Markdown文件: {file_name_base}")
        
        # 确保报告目录存在
        report_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        os.makedirs(report_dir, exist_ok=True)
        print(f"报告保存路径: {report_dir}")
        
        # 获取不带路径和扩展名的文件名（仅文件名部分）
        base_filename = os.path.splitext(os.path.basename(file_name_base))[0]
        
        # 构建Markdown文件路径
        md_path = os.path.join(report_dir, f"{base_filename}_analysis.md")
        
        # 组合完整的Markdown内容，包含文本内容、情感分析和总结
        full_content = f"# 语音文本分析报告\n\n## 原始文本内容\n\n{text_content}\n\n## 情感分析\n\n{analysis_result}\n\n## 总结\n\n{summary_result}"
        
        # 写入Markdown内容到文件
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(full_content)
            
        print(f"分析结果已保存为Markdown文件: {md_path}")
        return True, md_path
            
    except Exception as e:
        error_msg = f"将分析结果保存为Markdown文件时发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, error_msg

def md_to_word(md_file_path, suffix='_analysis'):
    """
    将Markdown文件转换为Word文档
    
    Args:
        md_file_path (str): Markdown文件路径
        suffix (str): 输出文件名后缀，默认为'_analysis'
        
    Returns:
        tuple: (成功标志, 文件路径或错误消息)
    """
    try:
        print(f"开始将Markdown文件转换为Word文档: {md_file_path}")
        
        # 导入markdown模块中的转换函数
        from markdown import convert_md_to_word
        
        # 调用转换函数
        result = convert_md_to_word(md_file_path, suffix)
        
        # 检查结果
        if isinstance(result, str) and result.startswith("转换失败"):
            print(f"转换失败: {result}")
            return False, result
        else:
            print(f"转换成功，Word文件保存为: {result}")
            return True, result
            
    except Exception as e:
        error_msg = f"将Markdown文件转换为Word文档时发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, error_msg

def convert_summary_to_word(summary_result, file_name_base):
    """
    只将二次总结结果转换为Word文档
    
    Args:
        summary_result (str): 总结结果
        file_name_base (str): 输出文件名基础（不含扩展名）
    
    Returns:
        tuple: (成功标志, 文件路径或错误消息)
    """
    try:
        import os
        from config import REPORTS_CONFIG
        
        print(f"开始将总结结果转换为Word文档: {file_name_base}")
        
        # 确保报告目录存在
        report_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        os.makedirs(report_dir, exist_ok=True)
        print(f"报告保存路径: {report_dir}")
        
        # 获取不带路径和扩展名的文件名（仅文件名部分）
        base_filename = os.path.splitext(os.path.basename(file_name_base))[0]
        
        # 构建临时Markdown文件路径
        temp_md_path = os.path.join(report_dir, f"{base_filename}_summary.md")
        
        # 处理总结内容，仅移除可能的重复标题
        cleaned_summary = summary_result
        
        # 仅移除可能重复的顶级标题，保留所有其他格式和内容
        title_patterns = [
            "# 语音文本情感分析报告\n", 
            "# 语音文本总结报告\n",
            "语音文本情感分析报告\n",
            "语音文本总结报告\n",
            "# 总结报告\n",
            "总结报告\n",
            "# 语音文本\n",
            "语音文本\n"
        ]
        
        # 仅在文档开头查找和替换这些标题
        for pattern in title_patterns:
            if cleaned_summary.startswith(pattern):
                cleaned_summary = cleaned_summary[len(pattern):]
                break
        
        # 清理前面可能的空行，但保留其他所有格式
        cleaned_summary = cleaned_summary.strip()
        
        # 添加单一标题
        content = f"# 语音文本情感分析报告\n\n{cleaned_summary}"
        
        # 写入Markdown内容到临时文件
        with open(temp_md_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        # 使用convert_with_spire函数处理转换
        from markdown import MarkdownConverter
        
        converter = MarkdownConverter()
        output_file = os.path.join(report_dir, f"{base_filename}_summary.docx")
        
        success, message = converter.convert_with_spire(temp_md_path, output_file)
        
        # 删除临时Markdown文件
        if os.path.exists(temp_md_path):
            os.remove(temp_md_path)
            
        if success:
            # 清除评估警告
            converter.remove_evaluation_warning(output_file)
            # 应用格式设置
            converter.format_word_document(output_file)
            return True, output_file
        else:
            return False, message
            
    except Exception as e:
        error_msg = f"将总结结果转换为Word文档时发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, error_msg

def extract_emotion_result(summary_result):
    """
    从总结结果中提取"经分析，当前主体情感具有（xxx）的心理特征"这部分文本
    
    Args:
        summary_result (str): 总结结果
        
    Returns:
        str: 提取的情感特征文本，如果未找到则返回空字符串
    """
    try:
        # 使用正则表达式匹配"经分析，当前主体情感具有（xxx）的心理特征"模式
        import re
        pattern = r"经分析，当前主体情感具有（([^）]+)）的心理特征"
        match = re.search(pattern, summary_result)
        
        if match:
            emotions = match.group(1)  # 提取括号中的情感词汇
            result = f"经分析，当前主体情感具有（{emotions}）的心理特征"
            print(f"成功提取情感特征: {result}")
            return result
        else:
            # 尝试匹配可能的变体，比如没有顿号或使用不同的标点符号
            alt_pattern = r"经分析，当前主体情感具有\s*[（(]([^）)]+)[）)]\s*的心理特征"
            alt_match = re.search(alt_pattern, summary_result)
            
            if alt_match:
                emotions = alt_match.group(1)
                result = f"经分析，当前主体情感具有（{emotions}）的心理特征"
                print(f"成功提取情感特征(替代模式): {result}")
                return result
                
            # 如果还是找不到，尝试提取"核心结论"部分
            conclusion_pattern = r"核心结论：\s*\n*\s*经分析，当前主体情感具有[（(]([^）)]+)[）)]"
            conclusion_match = re.search(conclusion_pattern, summary_result)
            
            if conclusion_match:
                emotions = conclusion_match.group(1)
                result = f"经分析，当前主体情感具有（{emotions}）的心理特征"
                print(f"成功从核心结论提取情感特征: {result}")
                return result
            
            print(f"未能从总结中提取情感特征，将返回空结果")
            return ""
    except Exception as e:
        print(f"提取情感特征时出错: {str(e)}")
        return ""

def process_audio_text_analysis(wav_file_path, convert_to_word=True, summary_only=True):
    """
    完整的音频文本分析流程：语音转文本->情感分析->二次总结->可选:转换为Word
    
    Args:
        wav_file_path (str): WAV文件路径
        convert_to_word (bool): 是否转换为Word文档
        summary_only (bool): 如果转换为Word，是否只转换总结部分
    
    Returns:
        tuple: (成功标志, 文本内容, 分析结果, 总结结果, [Word文件路径])
    """
    try:
        # 1. 语音转文本
        text_content = yu_to_text(wav_file_path)
        if not text_content or text_content.startswith("错误") or text_content.startswith("失败"):
            return False, text_content, "", "", ""
        
        # 2. 情感分析
        analysis_result = analyze_emotion_with_llm(text_content)
        if not analysis_result or analysis_result.startswith("情感分析失败"):
            return False, text_content, analysis_result, "", ""
        
        # 3. 二次总结
        summary_result = summarize_analysis_with_llm(analysis_result)
        if not summary_result or summary_result.startswith("总结失败"):
            return False, text_content, analysis_result, summary_result, ""
        
        # 4. 可选：转换为Word
        word_file = ""
        if convert_to_word:
            if summary_only:
                # 只转换总结部分
                success, word_file = convert_summary_to_word(summary_result, wav_file_path)
            else:
                # 转换完整分析
                success, word_file = convert_analysis_to_word(text_content, analysis_result, summary_result, wav_file_path)
                
            if not success:
                print(f"转换Word文档失败: {word_file}")
        
        return True, text_content, analysis_result, summary_result, word_file
        
    except Exception as e:
        error_msg = f"音频文本分析流程发生异常: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, "", "", "", error_msg

if __name__ == '__main__':
    # 简单测试
    wav_dz = "111111.wav"
    result = yu_to_text(wav_dz)
    print(f"最终输出结果: {result}")
