<template>
  <div class="emotion-warning">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Warning /></el-icon>
            情感风险预警
          </h1>
          <p class="page-description">
            实时监控和展示包含危险情感的分析结果，帮助及时识别和处理潜在风险
          </p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Refresh" @click="refreshData" :loading="loading">
            刷新数据
          </el-button>
          <el-button type="warning" :icon="Download" @click="exportData">
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card critical">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ criticalRiskCount }}</div>
                <div class="stat-label">极高风险</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card high">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ highRiskCount }}</div>
                <div class="stat-label">高风险</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card medium">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ mediumRiskCount }}</div>
                <div class="stat-label">中风险</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card emotions">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ uniqueDangerousEmotions }}</div>
                <div class="stat-label">危险情感类型</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名或危险情感..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 300px; margin-right: 16px;"
            />
            <el-select
              v-model="riskLevelFilter"
              placeholder="风险等级"
              clearable
              @change="handleFilter"
              style="width: 140px; margin-right: 16px;"
            >
              <el-option label="极高风险" value="critical" />
              <el-option label="高风险" value="high" />
              <el-option label="中风险" value="medium" />
              <el-option label="低风险" value="low" />
            </el-select>
            <el-select
              v-model="emotionTypeFilter"
              placeholder="情感类型"
              clearable
              @change="handleFilter"
              style="width: 150px;"
            >
              <el-option
                v-for="emotion in allDangerousEmotions"
                :key="emotion"
                :label="emotion"
                :value="emotion"
              />
            </el-select>
          </div>
          <div class="filter-right">
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="table-header">
            <span class="table-title">危险情感文件列表</span>
            <div class="table-actions">
              <el-button-group>
                <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                  <el-icon><Grid /></el-icon>
                  表格视图
                </el-button>
                <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                  <el-icon><Postcard /></el-icon>
                  卡片视图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <el-table
            :data="filteredFileList"
            :loading="loading"
            stripe
            @sort-change="handleSortChange"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" sortable />
            <el-table-column prop="filename" label="文件名" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="filename-cell">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span>{{ row.filename }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="upload_time" label="上传时间" width="180" sortable />
            <el-table-column label="风险等级" width="120">
              <template #default="{ row }">
                <el-tag :type="getRiskLevelType(row)" :effect="getRiskLevelEffect(row)">
                  {{ getRiskLevel(row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="危险情感" min-width="250">
              <template #default="{ row }">
                <div class="emotion-tags">
                  <el-tag
                    v-for="(emotion, index) in detectDangerousEmotions(row.analysis_result)"
                    :key="index"
                    type="danger"
                    size="small"
                    effect="dark"
                    class="emotion-tag"
                  >
                    {{ emotion }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="viewDetails(row)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button type="warning" size="small" @click="downloadAnalysis(row)">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button type="danger" size="small" @click="markAsHandled(row)">
                    <el-icon><Check /></el-icon>
                    已处理
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="16">
            <el-col :span="8" v-for="file in filteredFileList" :key="file.id">
              <el-card class="file-card" :class="getRiskLevelClass(file)">
                <template #header>
                  <div class="card-header">
                    <div class="file-info">
                      <el-icon class="file-icon"><Document /></el-icon>
                      <span class="filename" :title="file.filename">{{ file.filename }}</span>
                    </div>
                    <el-tag :type="getRiskLevelType(file)" size="small">
                      {{ getRiskLevel(file) }}
                    </el-tag>
                  </div>
                </template>
                <div class="card-content">
                  <div class="file-meta">
                    <p><el-icon><Clock /></el-icon> {{ file.upload_time }}</p>
                    <p><el-icon><Files /></el-icon> {{ formatFileSize(file.filesize) }}</p>
                  </div>
                  <div class="emotions-section">
                    <div class="section-title">检测到的危险情感：</div>
                    <div class="emotion-tags">
                      <el-tag
                        v-for="(emotion, index) in detectDangerousEmotions(file.analysis_result)"
                        :key="index"
                        type="danger"
                        size="small"
                        class="emotion-tag"
                      >
                        {{ emotion }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="card-actions">
                    <el-button type="primary" size="small" @click="viewDetails(file)">详情</el-button>
                    <el-button type="warning" size="small" @click="downloadAnalysis(file)">下载</el-button>
                    <el-button type="success" size="small" @click="markAsHandled(file)">已处理</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="currentFile?.filename || '文件详情'"
      size="60%"
      direction="rtl"
    >
      <div v-if="currentFile" class="detail-content">
        <!-- 文件基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-title">
              <el-icon><InfoFilled /></el-icon>
              基本信息
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentFile.filename }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(currentFile.filesize) }}</el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ currentFile.upload_time }}</el-descriptions-item>
            <el-descriptions-item label="分析状态">
              <el-tag :type="currentFile.status === '解析成功' ? 'success' : 'warning'">
                {{ currentFile.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="风险等级">
              <el-tag :type="getRiskLevelType(currentFile)" effect="dark">
                {{ getRiskLevel(currentFile) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="危险情感数量">
              {{ detectDangerousEmotions(currentFile.analysis_result).length }} 种
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 危险情感分析 -->
        <el-card class="emotions-card">
          <template #header>
            <div class="card-title">
              <el-icon><Warning /></el-icon>
              危险情感分析
            </div>
          </template>
          <div class="emotions-analysis">
            <div class="emotions-grid">
              <div
                v-for="(emotion, index) in detectDangerousEmotions(currentFile.analysis_result)"
                :key="index"
                class="emotion-item"
              >
                <el-tag type="danger" effect="dark" size="large">{{ emotion }}</el-tag>
                <div class="emotion-description">{{ getEmotionDescription(emotion) }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 分析结果内容 -->
        <el-card class="analysis-card" v-if="analysisContent">
          <template #header>
            <div class="card-title">
              <el-icon><Document /></el-icon>
              完整分析结果
            </div>
          </template>
          <div class="analysis-content">
            <el-scrollbar height="300px">
              <pre class="analysis-text">{{ analysisContent }}</pre>
            </el-scrollbar>
          </div>
        </el-card>

        <!-- 操作建议 -->
        <el-card class="suggestions-card">
          <template #header>
            <div class="card-title">
              <el-icon><Lightbulb /></el-icon>
              处理建议
            </div>
          </template>
          <div class="suggestions-content">
            <el-alert
              :title="getSuggestionTitle(currentFile)"
              :type="getSuggestionType(currentFile)"
              :description="getSuggestionDescription(currentFile)"
              show-icon
              :closable="false"
            />
            <div class="suggestion-actions">
              <el-button type="primary" @click="downloadAnalysis(currentFile)">
                <el-icon><Download /></el-icon>
                下载完整报告
              </el-button>
              <el-button type="success" @click="markAsHandled(currentFile)">
                <el-icon><Check /></el-icon>
                标记为已处理
              </el-button>
              <el-button type="warning" @click="escalateRisk(currentFile)">
                <el-icon><Top /></el-icon>
                上报风险
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Warning,
  Refresh,
  Download,
  CircleClose,
  WarningFilled,
  DataAnalysis,
  Search,
  Grid,
  Postcard,
  Document,
  View,
  Check,
  Clock,
  Files,
  InfoFilled,
  Lightbulb,
  Top
} from '@element-plus/icons-vue';
import axios from 'axios';
import { apiBaseUrl } from '@/config';

export default defineComponent({
  name: 'EmotionWarning',
  components: {
    Warning,
    Refresh,
    Download,
    CircleClose,
    WarningFilled,
    DataAnalysis,
    Search,
    Grid,
    Postcard,
    Document,
    View,
    Check,
    Clock,
    Files,
    InfoFilled,
    Lightbulb,
    Top
  },
  setup() {
    // 响应式数据
    const fileList = ref([]);
    const loading = ref(false);
    const detailDrawerVisible = ref(false);
    const currentFile = ref(null);
    const analysisContent = ref('');
    const searchKeyword = ref('');
    const riskLevelFilter = ref('');
    const emotionTypeFilter = ref('');
    const viewMode = ref('table');
    const autoRefresh = ref(false);
    const refreshTimer = ref(null);

    // 基于后端lj.txt文件的完整危险情感分类系统
    const dangerousEmotions = [
      '消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', '愤怒', '敌视',
      '报复心理', '强迫观念', '疑病', '自暴自弃', '自满', '自负', '自卑', '偏执',
      '孤独', '不合群', '依赖', '从众心理', '猜忌', '嫉妒', '敏感', '空虚',
      '压抑', '逆反', '逆来顺受', '懒散', '后悔', '愧疚', '麻木', '逃避现实',
      '报复', '羞耻', '执念', '易怒', '冷漠', '贪婪', '虚荣', '成瘾性依赖',
      '精神内耗'
    ];

    // 极高风险情感（需要立即干预，可能导致极端行为）
    const criticalRiskEmotions = [
      '绝望', '自暴自弃', '报复心理', '报复', '强迫观念', '麻木', '成瘾性依赖'
    ];

    // 高风险情感（需要重点关注和专业干预）
    const highRiskEmotions = [
      '抑郁', '恐慌', '愤怒', '敌视', '偏执', '逃避现实', '羞耻', '执念', '精神内耗'
    ];

    // 中风险情感（需要关注和适当疏导）
    const mediumRiskEmotions = [
      '焦虑', '恐惧', '悲观', '疑病', '自满', '自负', '自卑', '孤独', '不合群',
      '猜忌', '嫉妒', '空虚', '压抑', '逆反', '易怒', '冷漠', '贪婪', '虚荣'
    ];

    // 低风险情感（需要一般关注）
    const lowRiskEmotions = [
      '消极', '依赖', '从众心理', '敏感', '逆来顺受', '懒散', '后悔', '愧疚'
    ];

    // 安全情感列表（用于对比分析）
    const safeEmotions = [
      '积极', '希望', '乐观', '信心', '感恩', '信任', '挑战', '怀旧',
      '平静', '喜悦', '共情', '宽恕', '敬畏', '满足', '勇气', '好奇', '奉献'
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    // 计算属性
    const totalDangerousFiles = computed(() => fileList.value.length);

    const criticalRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '极高风险').length;
    });

    const highRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '高风险').length;
    });

    const mediumRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '中风险').length;
    });

    const lowRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '低风险').length;
    });

    const uniqueDangerousEmotions = computed(() => {
      const emotions = new Set();
      fileList.value.forEach(file => {
        detectDangerousEmotions(file.analysis_result).forEach(emotion => {
          emotions.add(emotion);
        });
      });
      return emotions.size;
    });

    const allDangerousEmotions = computed(() => {
      const emotions = new Set();
      fileList.value.forEach(file => {
        detectDangerousEmotions(file.analysis_result).forEach(emotion => {
          emotions.add(emotion);
        });
      });
      return Array.from(emotions).sort();
    });

    const filteredFileList = computed(() => {
      let filtered = [...fileList.value];

      // 搜索过滤
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        filtered = filtered.filter(file =>
          file.filename.toLowerCase().includes(keyword) ||
          detectDangerousEmotions(file.analysis_result).some(emotion =>
            emotion.toLowerCase().includes(keyword)
          )
        );
      }

      // 风险等级过滤
      if (riskLevelFilter.value) {
        filtered = filtered.filter(file => {
          const level = getRiskLevel(file);
          switch (riskLevelFilter.value) {
            case 'critical': return level === '极高风险';
            case 'high': return level === '高风险';
            case 'medium': return level === '中风险';
            case 'low': return level === '低风险';
            default: return true;
          }
        });
      }

      // 情感类型过滤
      if (emotionTypeFilter.value) {
        filtered = filtered.filter(file =>
          detectDangerousEmotions(file.analysis_result).includes(emotionTypeFilter.value)
        );
      }

      return filtered;
    });

    // 工具方法 - 基于后端lj.txt的风险分级逻辑
    const getRiskLevel = (file) => {
      const emotions = detectDangerousEmotions(file.analysis_result);

      // 检查是否包含极高风险情感
      const hasCriticalRisk = emotions.some(emotion => criticalRiskEmotions.includes(emotion));
      if (hasCriticalRisk) return '极高风险';

      // 检查是否包含高风险情感
      const hasHighRisk = emotions.some(emotion => highRiskEmotions.includes(emotion));
      if (hasHighRisk) return '高风险';

      // 检查是否包含中风险情感
      const hasMediumRisk = emotions.some(emotion => mediumRiskEmotions.includes(emotion));
      if (hasMediumRisk) return '中风险';

      // 其余为低风险
      return '低风险';
    };

    const getRiskLevelType = (file) => {
      const level = getRiskLevel(file);
      switch (level) {
        case '极高风险': return 'danger';
        case '高风险': return 'danger';
        case '中风险': return 'warning';
        case '低风险': return 'info';
        default: return 'info';
      }
    };

    const getRiskLevelEffect = (file) => {
      const level = getRiskLevel(file);
      return (level === '极高风险' || level === '高风险') ? 'dark' : 'plain';
    };

    const getRiskLevelClass = (file) => {
      const level = getRiskLevel(file);
      switch (level) {
        case '极高风险': return 'critical-risk';
        case '高风险': return 'high-risk';
        case '中风险': return 'medium-risk';
        case '低风险': return 'low-risk';
        default: return 'low-risk';
      }
    };

    const getEmotionDescription = (emotion) => {
      // 基于lj.txt文件的专业情感描述
      const descriptions = {
        // 极高风险情感
        '绝望': '丧失对未来的希望，可能导致放弃自我或极端行为，需要立即心理干预',
        '自暴自弃': '放弃自我管理与努力，导致状态持续恶化，需要专业支持',
        '报复心理': '以伤害他人为目标的心态，可能导致破坏性行为，需要立即关注',
        '报复': '通过伤害他人宣泄情绪，可能引发法律或人际危机，需要紧急干预',
        '强迫观念': '反复出现的强迫性思维，伴随痛苦，属于心理障碍，需要专业治疗',
        '麻木': '情感淡漠，失去感知与反应能力，是心理防御过度的表现',
        '成瘾性依赖': '通过物质或行为逃避现实，形成病态依赖，需要专业戒断治疗',

        // 高风险情感
        '抑郁': '持续的情绪低落，伴随兴趣减退，属于心理疾病范畴，需要专业治疗',
        '恐慌': '对特定事物或情境的过度害怕，可能引发逃避或应激障碍',
        '愤怒': '强烈的敌对情绪，易引发冲突，损害人际关系与理性判断',
        '敌视': '强烈的敌对情绪，易引发冲突，损害人际关系与理性判断',
        '偏执': '固执己见，多疑且不信任他人，易陷入人际孤立',
        '逃避现实': '拒绝面对问题，导致困难积累，加剧心理负担',
        '羞耻': '因自我否定产生的屈辱感，可能导致自我价值感崩溃',
        '执念': '对人或事的非理性执着，忽视现实合理性，引发偏执行为',
        '精神内耗': '过度自我批判或纠结，消耗心理能量却无实质行动',

        // 中风险情感
        '焦虑': '过度担忧未来，伴随生理紧张，影响正常生活功能',
        '恐惧': '对特定事物或情境的过度害怕，可能引发逃避行为',
        '悲观': '对未来持负面预期，易引发绝望感与退缩行为',
        '疑病': '过度担忧自身健康，陷入焦虑循环，影响生活质量',
        '自满': '过度高估自身，忽视不足，阻碍进步与人际关系',
        '自负': '过度高估自身，忽视不足，阻碍进步与人际关系',
        '自卑': '过低评价自我，引发自我否定与社交退缩',
        '孤独': '长期缺乏社交连接，可能导致情绪低落与心理疏离',
        '不合群': '长期缺乏社交连接，可能导致情绪低落与心理疏离',
        '猜忌': '无根据的怀疑，破坏信任关系，引发矛盾',
        '嫉妒': '对他人优势的负面情绪，伴随不满与怨恨，损害心态',
        '空虚': '缺乏意义感与目标，可能导致行为偏差或成瘾行为',
        '压抑': '压抑情绪或需求，导致心理能量积压，可能引发身心疾病',
        '逆反': '盲目抗拒规则或他人建议，影响理性决策与人际关系',
        '易怒': '情绪阈值降低，微小刺激即爆发愤怒，损害人际关系与健康',
        '冷漠': '情感疏离、缺乏同理心，导致社交孤立与自我封闭',
        '贪婪': '过度追求欲望满足，忽视道德与现实限制，引发心理失衡',
        '虚荣': '依赖外界评价维持自尊，易陷入焦虑与自我欺骗',

        // 低风险情感
        '消极': '缺乏动力，倾向于关注事物的负面，阻碍积极行动',
        '依赖': '过度依靠他人，丧失自主性，易在关系中感到失控',
        '从众心理': '盲目跟随他人，缺乏独立判断，可能导致非理性行为',
        '敏感': '过度感知外界刺激，易陷入焦虑或自我内耗',
        '逆来顺受': '被动承受压力，缺乏自我保护意识，可能加剧心理损耗',
        '懒散': '缺乏行动力，导致目标拖延，引发自我否定与挫败感',
        '后悔': '过度沉溺于过去的失误，消耗心理能量，影响当下状态',
        '愧疚': '过度沉溺于过去的失误，消耗心理能量，影响当下状态'
      };

      return descriptions[emotion] || '需要关注的负面情感，建议进行心理评估';
    };

    // 加载数据
    const loadData = async (params = {}) => {
      loading.value = true;
      try {
        const { page = 1, pageSize = 10, sortField, sortOrder } = params;

        // 调用后端API获取危险情感文件数据
        const response = await axios.get(`${apiBaseUrl}/api/files/dangerous-emotions`, {
          params: {
            page,
            page_size: pageSize,
            sort_field: sortField || 'id',
            sort_order: sortOrder || 'desc',
          },
        });

        const { data, total } = response.data;

        fileList.value = data || [];
        pagination.total = total || 0;
      } catch (error) {
        console.error('获取危险情感文件列表失败:', error);
        ElMessage.error('获取数据失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 刷新数据
    const refreshData = () => {
      loadData();
      ElMessage.success('数据已刷新');
    };

    // 事件处理方法
    const handleSearch = () => {
      // 搜索在计算属性中处理，这里可以添加防抖逻辑
    };

    const handleFilter = () => {
      // 过滤在计算属性中处理
    };

    const handleSortChange = ({ prop, order }) => {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        sortField: prop,
        sortOrder: order === 'ascending' ? 'asc' : 'desc',
      };
      loadData(params);
    };

    const handleSizeChange = (size) => {
      pagination.pageSize = size;
      pagination.current = 1;
      loadData();
    };

    const handleCurrentChange = (page) => {
      pagination.current = page;
      loadData();
    };

    // 自动刷新控制
    const toggleAutoRefresh = (enabled) => {
      if (enabled) {
        refreshTimer.value = setInterval(() => {
          loadData();
        }, 30000); // 30秒刷新一次
        ElMessage.success('已开启自动刷新（30秒间隔）');
      } else {
        if (refreshTimer.value) {
          clearInterval(refreshTimer.value);
          refreshTimer.value = null;
        }
        ElMessage.info('已关闭自动刷新');
      }
    };

    // 查看详情
    const viewDetails = async (record) => {
      currentFile.value = record;
      detailDrawerVisible.value = true;

      try {
        // 获取分析结果内容
        const response = await axios.get(`${apiBaseUrl}/api/files/${record.id}/analysis-content`);
        analysisContent.value = response.data.content || response.data.summary || '暂无详细内容';
      } catch (error) {
        console.error('获取分析结果内容失败:', error);
        ElMessage.error('获取分析结果内容失败');
        analysisContent.value = '无法加载分析结果内容';
      }
    };

    // 下载分析报告
    const downloadAnalysis = async (file) => {
      try {
        const response = await axios.get(`${apiBaseUrl}/api/files/${file.id}/download_result`, {
          responseType: 'blob'
        });

        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${file.filename}_分析报告.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('报告下载成功');
      } catch (error) {
        console.error('下载报告失败:', error);
        ElMessage.error('下载报告失败，请稍后重试');
      }
    };

    // 标记为已处理
    const markAsHandled = async (file) => {
      try {
        await ElMessageBox.confirm(
          `确定要将文件 "${file.filename}" 标记为已处理吗？`,
          '确认操作',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        // 这里可以调用后端API标记文件状态
        // await axios.put(`${apiBaseUrl}/api/files/${file.id}/mark-handled`);

        ElMessage.success('已标记为已处理');
        loadData(); // 重新加载数据
      } catch (error) {
        if (error !== 'cancel') {
          console.error('标记处理状态失败:', error);
          ElMessage.error('操作失败，请稍后重试');
        }
      }
    };

    // 上报风险
    const escalateRisk = async (file) => {
      try {
        await ElMessageBox.confirm(
          `确定要上报文件 "${file.filename}" 的风险吗？这将通知相关负责人。`,
          '风险上报',
          {
            confirmButtonText: '确定上报',
            cancelButtonText: '取消',
            type: 'error',
          }
        );

        // 这里可以调用后端API上报风险
        // await axios.post(`${apiBaseUrl}/api/files/${file.id}/escalate`);

        ElMessage.success('风险已上报，相关人员将收到通知');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('上报风险失败:', error);
          ElMessage.error('上报失败，请稍后重试');
        }
      }
    };

    // 导出数据
    const exportData = () => {
      try {
        const csvContent = generateCSVContent();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `危险情感文件报告_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('报告导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        ElMessage.error('导出失败，请稍后重试');
      }
    };

    // 生成CSV内容
    const generateCSVContent = () => {
      const headers = ['文件名', '上传时间', '风险等级', '危险情感', '文件大小'];
      const rows = filteredFileList.value.map(file => [
        file.filename,
        file.upload_time,
        getRiskLevel(file),
        detectDangerousEmotions(file.analysis_result).join(';'),
        formatFileSize(file.filesize)
      ]);

      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      return '\uFEFF' + csvContent; // 添加BOM以支持中文
    };

    // 获取建议 - 基于后端lj.txt的专业分级建议
    const getSuggestionTitle = (file) => {
      const level = getRiskLevel(file);
      switch (level) {
        case '极高风险': return '紧急干预建议';
        case '高风险': return '重点关注建议';
        case '中风险': return '适度关注建议';
        case '低风险': return '一般关注建议';
        default: return '关注建议';
      }
    };

    const getSuggestionType = (file) => {
      const level = getRiskLevel(file);
      switch (level) {
        case '极高风险': return 'error';
        case '高风险': return 'error';
        case '中风险': return 'warning';
        case '低风险': return 'info';
        default: return 'info';
      }
    };

    const getSuggestionDescription = (file) => {
      const level = getRiskLevel(file);
      const emotions = detectDangerousEmotions(file.analysis_result);

      switch (level) {
        case '极高风险':
          return `检测到极高风险情感：${emotions.join('、')}。这些情感可能导致极端行为或严重心理问题，建议立即联系心理健康专业人员进行紧急干预，必要时考虑住院治疗或24小时监护。同时应通知相关负责人和家属，制定详细的安全保护计划。`;

        case '高风险':
          return `检测到高风险情感：${emotions.join('、')}。这些情感可能严重影响心理健康和日常功能，建议尽快安排专业心理咨询或治疗，加强日常关注和支持，定期评估心理状态变化，必要时考虑药物治疗辅助。`;

        case '中风险':
          return `检测到中等风险情感：${emotions.join('、')}。这些情感需要适度关注和疏导，建议提供心理支持和咨询服务，帮助建立健康的应对机制，定期跟进心理状态，预防情况恶化。`;

        case '低风险':
          return `检测到轻度负面情感：${emotions.join('、')}。这些情感属于正常范围内的心理波动，建议保持关注，提供适当的情感支持和理解，鼓励积极的生活方式和社交活动。`;

        default:
          return `检测到负面情感：${emotions.join('、')}。建议进行进一步的心理评估，确定具体的风险等级和干预措施。`;
      }
    };

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B';

      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let index = 0;
      let formattedSize = size;

      while (formattedSize >= 1024 && index < units.length - 1) {
        formattedSize /= 1024;
        index++;
      }

      return `${formattedSize.toFixed(2)} ${units[index]}`;
    };

    // 检测危险情感 - 与后端逻辑保持一致
    const detectDangerousEmotions = (content) => {
      if (!content) return [];

      let emotions = [];

      // 首先尝试使用正则表达式提取情感词汇（与后端保持一致）
      const pattern = /经分析，当前主体情感具有[（(]([^）)]+)[）)]/;
      const match = content.match(pattern);

      if (match) {
        // 提取括号中的情感词汇，分成列表
        const emotionsStr = match[1];
        emotions = emotionsStr.split(/[,，、]/).map(e => e.trim()).filter(e => e);

        // 只返回危险情感
        emotions = emotions.filter(emotion => dangerousEmotions.includes(emotion));
      }

      // 如果没有通过正则表达式匹配到，则直接搜索文本中的危险情感关键词
      if (emotions.length === 0) {
        emotions = dangerousEmotions.filter(emotion => content.includes(emotion));
      }

      // 去重并返回
      return [...new Set(emotions)];
    };

    // 组件挂载和卸载
    onMounted(() => {
      loadData();
    });

    onUnmounted(() => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
      }
    });

    return {
      // 响应式数据
      fileList,
      loading,
      detailDrawerVisible,
      currentFile,
      analysisContent,
      searchKeyword,
      riskLevelFilter,
      emotionTypeFilter,
      viewMode,
      autoRefresh,
      pagination,

      // 计算属性
      totalDangerousFiles,
      criticalRiskCount,
      highRiskCount,
      mediumRiskCount,
      lowRiskCount,
      uniqueDangerousEmotions,
      allDangerousEmotions,
      filteredFileList,

      // 方法
      refreshData,
      handleSearch,
      handleFilter,
      handleSortChange,
      handleSizeChange,
      handleCurrentChange,
      toggleAutoRefresh,
      viewDetails,
      downloadAnalysis,
      markAsHandled,
      escalateRisk,
      exportData,
      getRiskLevel,
      getRiskLevelType,
      getRiskLevelEffect,
      getRiskLevelClass,
      getEmotionDescription,
      getSuggestionTitle,
      getSuggestionType,
      getSuggestionDescription,
      formatFileSize,
      detectDangerousEmotions,
    };
  },
});
</script>

<style scoped>
.emotion-warning {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #f56c6c;
  font-size: 32px;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计概览 */
.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.critical {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.stat-card.high {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.stat-card.medium {
  background: linear-gradient(135deg, #ffb74d, #ff9800);
  color: white;
}

.stat-card.emotions {
  background: linear-gradient(135deg, #64b5f6, #2196f3);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
}

/* 表格区域 */
.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
  font-size: 16px;
}

.emotion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.emotion-tag {
  margin: 2px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 卡片视图 */
.card-view {
  margin-bottom: 24px;
}

.file-card {
  margin-bottom: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.file-card.critical-risk {
  border-color: #dc2626;
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
  animation: criticalPulse 3s infinite;
}

@keyframes criticalPulse {
  0%, 100% { box-shadow: 0 0 20px rgba(220, 38, 38, 0.3); }
  50% { box-shadow: 0 0 30px rgba(220, 38, 38, 0.5); }
}

.file-card.high-risk {
  border-color: #f56c6c;
}

.file-card.medium-risk {
  border-color: #e6a23c;
}

.file-card.low-risk {
  border-color: #67c23a;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.filename {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-content {
  padding: 16px 0 0 0;
}

.file-meta {
  margin-bottom: 16px;
}

.file-meta p {
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.emotions-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 分页 */
.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* 详情抽屉 */
.detail-content {
  padding: 0;
}

.info-card,
.emotions-card,
.analysis-card,
.suggestions-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.emotions-analysis {
  padding: 16px 0;
}

.emotions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.emotion-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.emotion-description {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.analysis-content {
  padding: 16px 0;
}

.analysis-text {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.suggestions-content {
  padding: 16px 0;
}

.suggestion-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .emotion-warning {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .action-section {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .filter-left {
    width: 100%;
    flex-wrap: wrap;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .overview-section :deep(.el-col) {
    margin-bottom: 16px;
  }

  .card-view :deep(.el-col) {
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .stat-value {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .card-actions {
    flex-direction: column;
    gap: 8px;
  }

  .suggestion-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .emotion-warning {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .stat-content {
    padding: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
    margin-right: 12px;
  }

  .emotions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
