<template>
  <div class="emotion-warning">
    <div class="page-header">
      <h1>情感风险警告</h1>
      <div class="page-description">
        此页面展示分析结果中包含危险情感的文件列表，请关注并及时处理。
      </div>
    </div>

    <!-- 表格显示区域 -->
    <div class="data-table-container">
      <a-table
        :columns="columns"
        :data-source="fileList"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
        rowKey="id"
      >
        <!-- 文件名列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'filename'">
            <a-tooltip :title="record.filename">
              {{ record.filename }}
            </a-tooltip>
          </template>

          <!-- 危险情感标签列 -->
          <template v-if="column.dataIndex === 'dangerous_emotions'">
            <div class="emotion-tags">
              <a-tag 
                v-for="(emotion, index) in detectDangerousEmotions(record.analysis_result)"
                :key="index"
                color="red"
              >
                {{ emotion }}
              </a-tag>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'operation'">
            <div class="operation-buttons">
              <a-button type="primary" size="small" @click="viewDetails(record)">
                查看详情
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 详情模态框 -->
    <a-modal
      v-model:visible="detailModalVisible"
      :title="currentFile ? currentFile.filename : '文件详情'"
      width="800px"
      @cancel="closeDetailModal"
    >
      <template v-if="currentFile">
        <div class="file-details">
          <p><strong>文件名：</strong> {{ currentFile.filename }}</p>
          <p><strong>上传时间：</strong> {{ currentFile.upload_time }}</p>
          <p><strong>文件大小：</strong> {{ formatFileSize(currentFile.filesize) }}</p>
          <p><strong>状态：</strong> {{ currentFile.status }}</p>
          <p><strong>识别到的危险情感：</strong></p>
          <div class="emotion-tags">
            <a-tag 
              v-for="(emotion, index) in detectDangerousEmotions(currentFile.analysis_result)"
              :key="index"
              color="red"
            >
              {{ emotion }}
            </a-tag>
          </div>
          <div class="analysis-result" v-if="analysisContent">
            <h3>分析结果内容：</h3>
            <div class="result-content">{{ analysisContent }}</div>
          </div>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { apiBaseUrl } from '@/config';

export default defineComponent({
  name: 'EmotionWarning',
  setup() {
    // 表格列定义
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
        sorter: true,
      },
      {
        title: '文件名',
        dataIndex: 'filename',
        ellipsis: true,
        sorter: true,
      },
      {
        title: '上传时间',
        dataIndex: 'upload_time',
        width: 180,
        sorter: true,
      },
      {
        title: '危险情感',
        dataIndex: 'dangerous_emotions',
        width: 200,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        fixed: 'right',
      },
    ];

    // 状态变量
    const fileList = ref([]);
    const loading = ref(false);
    const detailModalVisible = ref(false);
    const currentFile = ref(null);
    const analysisContent = ref('');

    // 危险情感列表
    const dangerousEmotions = [
      '消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', '愤怒', '敌视',
      '报复心理', '强迫观念', '疑病', '自暴自弃', '自满', '自负', '自卑', '偏执',
      '孤独', '不合群', '依赖', '从众心理', '猜忌', '嫉妒', '敏感', '空虚',
      '压抑', '逆反', '逆来顺受', '懒散', '后悔', '愧疚', '麻木', '逃避现实',
      '报复', '羞耻', '执念', '易怒', '冷漠', '贪婪', '虚荣', '成瘾性依赖',
      '精神内耗'
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条记录`,
    });

    // 加载数据
    const loadData = async (params = {}) => {
      loading.value = true;
      try {
        const { page = 1, pageSize = 10, sortField, sortOrder } = params;
        
        // 调用后端API获取危险情感文件数据
        const response = await axios.get(`${apiBaseUrl}/api/files/dangerous-emotions`, {
          params: {
            page,
            page_size: pageSize,
            sort_field: sortField || 'id',
            sort_order: sortOrder || 'desc',
          },
        });

        const { data, total } = response.data;
        
        fileList.value = data;
        pagination.total = total;
      } catch (error) {
        console.error('获取危险情感文件列表失败:', error);
        message.error('获取数据失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 表格变化事件处理
    const handleTableChange = (pag, filters, sorter) => {
      const params = {
        page: pag.current,
        pageSize: pag.pageSize,
        sortField: sorter.field,
        sortOrder: sorter.order,
      };
      loadData(params);
    };

    // 查看详情
    const viewDetails = async (record) => {
      currentFile.value = record;
      detailModalVisible.value = true;
      
      try {
        // 获取分析结果内容
        const response = await axios.get(`${apiBaseUrl}/api/files/${record.id}/analysis-content`);
        analysisContent.value = response.data.content;
      } catch (error) {
        console.error('获取分析结果内容失败:', error);
        message.error('获取分析结果内容失败');
        analysisContent.value = '无法加载分析结果内容';
      }
    };

    // 关闭详情模态框
    const closeDetailModal = () => {
      detailModalVisible.value = false;
      currentFile.value = null;
      analysisContent.value = '';
    };

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B';
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let index = 0;
      let formattedSize = size;
      
      while (formattedSize >= 1024 && index < units.length - 1) {
        formattedSize /= 1024;
        index++;
      }
      
      return `${formattedSize.toFixed(2)} ${units[index]}`;
    };

    // 检测危险情感
    const detectDangerousEmotions = (content) => {
      if (!content) return [];
      
      // 在内容中检测危险情感关键词
      return dangerousEmotions.filter(emotion => {
        return content.includes(emotion);
      });
    };

    // 组件挂载时加载数据
    onMounted(() => {
      loadData();
    });

    return {
      columns,
      fileList,
      loading,
      pagination,
      detailModalVisible,
      currentFile,
      analysisContent,
      handleTableChange,
      viewDetails,
      closeDetailModal,
      formatFileSize,
      detectDangerousEmotions,
    };
  },
});
</script>

<style scoped>
.emotion-warning {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-description {
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 16px;
}

.data-table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.emotion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.file-details {
  padding: 10px 0;
}

.analysis-result {
  margin-top: 20px;
}

.result-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
  white-space: pre-wrap;
}
</style>
