<template>
  <div class="emotion-warning">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Warning /></el-icon>
            情感风险预警
          </h1>
          <p class="page-description">
            实时监控和展示包含危险情感的分析结果，帮助及时识别和处理潜在风险
          </p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Refresh" @click="refreshData" :loading="loading">
            刷新数据
          </el-button>
          <el-button type="warning" :icon="Download" @click="exportData">
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card danger">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ totalDangerousFiles }}</div>
                <div class="stat-label">危险文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card high">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ highRiskCount }}</div>
                <div class="stat-label">高风险</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card medium">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ mediumRiskCount }}</div>
                <div class="stat-label">中风险</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card emotions">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ uniqueDangerousEmotions }}</div>
                <div class="stat-label">危险情感类型</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名或危险情感..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 300px; margin-right: 16px;"
            />
            <el-select
              v-model="riskLevelFilter"
              placeholder="风险等级"
              clearable
              @change="handleFilter"
              style="width: 120px; margin-right: 16px;"
            >
              <el-option label="高风险" value="high" />
              <el-option label="中风险" value="medium" />
              <el-option label="低风险" value="low" />
            </el-select>
            <el-select
              v-model="emotionTypeFilter"
              placeholder="情感类型"
              clearable
              @change="handleFilter"
              style="width: 150px;"
            >
              <el-option
                v-for="emotion in allDangerousEmotions"
                :key="emotion"
                :label="emotion"
                :value="emotion"
              />
            </el-select>
          </div>
          <div class="filter-right">
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="table-header">
            <span class="table-title">危险情感文件列表</span>
            <div class="table-actions">
              <el-button-group>
                <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                  <el-icon><Grid /></el-icon>
                  表格视图
                </el-button>
                <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                  <el-icon><Postcard /></el-icon>
                  卡片视图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <el-table
            :data="filteredFileList"
            :loading="loading"
            stripe
            @sort-change="handleSortChange"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" sortable />
            <el-table-column prop="filename" label="文件名" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="filename-cell">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span>{{ row.filename }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="upload_time" label="上传时间" width="180" sortable />
            <el-table-column label="风险等级" width="120">
              <template #default="{ row }">
                <el-tag :type="getRiskLevelType(row)" :effect="getRiskLevelEffect(row)">
                  {{ getRiskLevel(row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="危险情感" min-width="250">
              <template #default="{ row }">
                <div class="emotion-tags">
                  <el-tag
                    v-for="(emotion, index) in detectDangerousEmotions(row.analysis_result)"
                    :key="index"
                    type="danger"
                    size="small"
                    effect="dark"
                    class="emotion-tag"
                  >
                    {{ emotion }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="viewDetails(row)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button type="warning" size="small" @click="downloadAnalysis(row)">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button type="danger" size="small" @click="markAsHandled(row)">
                    <el-icon><Check /></el-icon>
                    已处理
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="16">
            <el-col :span="8" v-for="file in filteredFileList" :key="file.id">
              <el-card class="file-card" :class="getRiskLevelClass(file)">
                <template #header>
                  <div class="card-header">
                    <div class="file-info">
                      <el-icon class="file-icon"><Document /></el-icon>
                      <span class="filename" :title="file.filename">{{ file.filename }}</span>
                    </div>
                    <el-tag :type="getRiskLevelType(file)" size="small">
                      {{ getRiskLevel(file) }}
                    </el-tag>
                  </div>
                </template>
                <div class="card-content">
                  <div class="file-meta">
                    <p><el-icon><Clock /></el-icon> {{ file.upload_time }}</p>
                    <p><el-icon><Files /></el-icon> {{ formatFileSize(file.filesize) }}</p>
                  </div>
                  <div class="emotions-section">
                    <div class="section-title">检测到的危险情感：</div>
                    <div class="emotion-tags">
                      <el-tag
                        v-for="(emotion, index) in detectDangerousEmotions(file.analysis_result)"
                        :key="index"
                        type="danger"
                        size="small"
                        class="emotion-tag"
                      >
                        {{ emotion }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="card-actions">
                    <el-button type="primary" size="small" @click="viewDetails(file)">详情</el-button>
                    <el-button type="warning" size="small" @click="downloadAnalysis(file)">下载</el-button>
                    <el-button type="success" size="small" @click="markAsHandled(file)">已处理</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="currentFile?.filename || '文件详情'"
      size="60%"
      direction="rtl"
    >
      <div v-if="currentFile" class="detail-content">
        <!-- 文件基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-title">
              <el-icon><InfoFilled /></el-icon>
              基本信息
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentFile.filename }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(currentFile.filesize) }}</el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ currentFile.upload_time }}</el-descriptions-item>
            <el-descriptions-item label="分析状态">
              <el-tag :type="currentFile.status === '解析成功' ? 'success' : 'warning'">
                {{ currentFile.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="风险等级">
              <el-tag :type="getRiskLevelType(currentFile)" effect="dark">
                {{ getRiskLevel(currentFile) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="危险情感数量">
              {{ detectDangerousEmotions(currentFile.analysis_result).length }} 种
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 危险情感分析 -->
        <el-card class="emotions-card">
          <template #header>
            <div class="card-title">
              <el-icon><Warning /></el-icon>
              危险情感分析
            </div>
          </template>
          <div class="emotions-analysis">
            <div class="emotions-grid">
              <div
                v-for="(emotion, index) in detectDangerousEmotions(currentFile.analysis_result)"
                :key="index"
                class="emotion-item"
              >
                <el-tag type="danger" effect="dark" size="large">{{ emotion }}</el-tag>
                <div class="emotion-description">{{ getEmotionDescription(emotion) }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 分析结果内容 -->
        <el-card class="analysis-card" v-if="analysisContent">
          <template #header>
            <div class="card-title">
              <el-icon><Document /></el-icon>
              完整分析结果
            </div>
          </template>
          <div class="analysis-content">
            <el-scrollbar height="300px">
              <pre class="analysis-text">{{ analysisContent }}</pre>
            </el-scrollbar>
          </div>
        </el-card>

        <!-- 操作建议 -->
        <el-card class="suggestions-card">
          <template #header>
            <div class="card-title">
              <el-icon><Lightbulb /></el-icon>
              处理建议
            </div>
          </template>
          <div class="suggestions-content">
            <el-alert
              :title="getSuggestionTitle(currentFile)"
              :type="getSuggestionType(currentFile)"
              :description="getSuggestionDescription(currentFile)"
              show-icon
              :closable="false"
            />
            <div class="suggestion-actions">
              <el-button type="primary" @click="downloadAnalysis(currentFile)">
                <el-icon><Download /></el-icon>
                下载完整报告
              </el-button>
              <el-button type="success" @click="markAsHandled(currentFile)">
                <el-icon><Check /></el-icon>
                标记为已处理
              </el-button>
              <el-button type="warning" @click="escalateRisk(currentFile)">
                <el-icon><Top /></el-icon>
                上报风险
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Warning,
  Refresh,
  Download,
  CircleClose,
  WarningFilled,
  DataAnalysis,
  Search,
  Grid,
  Postcard,
  Document,
  View,
  Check,
  Clock,
  Files,
  InfoFilled,
  Lightbulb,
  Top
} from '@element-plus/icons-vue';
import axios from 'axios';
import { apiBaseUrl } from '@/config';

export default defineComponent({
  name: 'EmotionWarning',
  components: {
    Warning,
    Refresh,
    Download,
    CircleClose,
    WarningFilled,
    DataAnalysis,
    Search,
    Grid,
    Postcard,
    Document,
    View,
    Check,
    Clock,
    Files,
    InfoFilled,
    Lightbulb,
    Top
  },
  setup() {
    // 响应式数据
    const fileList = ref([]);
    const loading = ref(false);
    const detailDrawerVisible = ref(false);
    const currentFile = ref(null);
    const analysisContent = ref('');
    const searchKeyword = ref('');
    const riskLevelFilter = ref('');
    const emotionTypeFilter = ref('');
    const viewMode = ref('table');
    const autoRefresh = ref(false);
    const refreshTimer = ref(null);

    // 危险情感列表和风险等级定义
    const dangerousEmotions = [
      '消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', '愤怒', '敌视',
      '报复心理', '强迫观念', '疑病', '自暴自弃', '自满', '自负', '自卑', '偏执',
      '孤独', '不合群', '依赖', '从众心理', '猜忌', '嫉妒', '敏感', '空虚',
      '压抑', '逆反', '逆来顺受', '懒散', '后悔', '愧疚', '麻木', '逃避现实',
      '报复', '羞耻', '执念', '易怒', '冷漠', '贪婪', '虚荣', '成瘾性依赖',
      '精神内耗'
    ];

    // 高风险情感（需要特别关注）
    const highRiskEmotions = [
      '绝望', '抑郁', '自暴自弃', '报复心理', '报复', '愤怒', '敌视', '恐慌'
    ];

    // 中风险情感
    const mediumRiskEmotions = [
      '焦虑', '恐惧', '悲观', '偏执', '自负', '自卑', '嫉妒', '执念', '易怒'
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    // 计算属性
    const totalDangerousFiles = computed(() => fileList.value.length);

    const highRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '高风险').length;
    });

    const mediumRiskCount = computed(() => {
      return fileList.value.filter(file => getRiskLevel(file) === '中风险').length;
    });

    const uniqueDangerousEmotions = computed(() => {
      const emotions = new Set();
      fileList.value.forEach(file => {
        detectDangerousEmotions(file.analysis_result).forEach(emotion => {
          emotions.add(emotion);
        });
      });
      return emotions.size;
    });

    const allDangerousEmotions = computed(() => {
      const emotions = new Set();
      fileList.value.forEach(file => {
        detectDangerousEmotions(file.analysis_result).forEach(emotion => {
          emotions.add(emotion);
        });
      });
      return Array.from(emotions).sort();
    });

    const filteredFileList = computed(() => {
      let filtered = [...fileList.value];

      // 搜索过滤
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        filtered = filtered.filter(file =>
          file.filename.toLowerCase().includes(keyword) ||
          detectDangerousEmotions(file.analysis_result).some(emotion =>
            emotion.toLowerCase().includes(keyword)
          )
        );
      }

      // 风险等级过滤
      if (riskLevelFilter.value) {
        filtered = filtered.filter(file => {
          const level = getRiskLevel(file);
          return level === (riskLevelFilter.value === 'high' ? '高风险' :
                          riskLevelFilter.value === 'medium' ? '中风险' : '低风险');
        });
      }

      // 情感类型过滤
      if (emotionTypeFilter.value) {
        filtered = filtered.filter(file =>
          detectDangerousEmotions(file.analysis_result).includes(emotionTypeFilter.value)
        );
      }

      return filtered;
    });

    // 工具方法
    const getRiskLevel = (file) => {
      const emotions = detectDangerousEmotions(file.analysis_result);
      const hasHighRisk = emotions.some(emotion => highRiskEmotions.includes(emotion));
      const hasMediumRisk = emotions.some(emotion => mediumRiskEmotions.includes(emotion));

      if (hasHighRisk) return '高风险';
      if (hasMediumRisk) return '中风险';
      return '低风险';
    };

    const getRiskLevelType = (file) => {
      const level = getRiskLevel(file);
      return level === '高风险' ? 'danger' : level === '中风险' ? 'warning' : 'info';
    };

    const getRiskLevelEffect = (file) => {
      const level = getRiskLevel(file);
      return level === '高风险' ? 'dark' : 'plain';
    };

    const getRiskLevelClass = (file) => {
      const level = getRiskLevel(file);
      return level === '高风险' ? 'high-risk' : level === '中风险' ? 'medium-risk' : 'low-risk';
    };

    const getEmotionDescription = (emotion) => {
      const descriptions = {
        '绝望': '极度消极的心理状态，需要立即关注',
        '抑郁': '持续的情绪低落，可能影响日常生活',
        '焦虑': '过度担心和紧张，需要适当疏导',
        '愤怒': '强烈的负面情绪，可能导致冲动行为',
        '恐惧': '对未知或威胁的过度反应',
        '自卑': '对自我价值的负面认知',
        '孤独': '缺乏社会连接和支持',
        '报复': '危险的攻击性倾向，需要重点关注'
      };
      return descriptions[emotion] || '需要关注的负面情感';
    };

    // 加载数据
    const loadData = async (params = {}) => {
      loading.value = true;
      try {
        const { page = 1, pageSize = 10, sortField, sortOrder } = params;

        // 调用后端API获取危险情感文件数据
        const response = await axios.get(`${apiBaseUrl}/api/files/dangerous-emotions`, {
          params: {
            page,
            page_size: pageSize,
            sort_field: sortField || 'id',
            sort_order: sortOrder || 'desc',
          },
        });

        const { data, total } = response.data;

        fileList.value = data || [];
        pagination.total = total || 0;
      } catch (error) {
        console.error('获取危险情感文件列表失败:', error);
        ElMessage.error('获取数据失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 刷新数据
    const refreshData = () => {
      loadData();
      ElMessage.success('数据已刷新');
    };

    // 事件处理方法
    const handleSearch = () => {
      // 搜索在计算属性中处理，这里可以添加防抖逻辑
    };

    const handleFilter = () => {
      // 过滤在计算属性中处理
    };

    const handleSortChange = ({ prop, order }) => {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        sortField: prop,
        sortOrder: order === 'ascending' ? 'asc' : 'desc',
      };
      loadData(params);
    };

    const handleSizeChange = (size) => {
      pagination.pageSize = size;
      pagination.current = 1;
      loadData();
    };

    const handleCurrentChange = (page) => {
      pagination.current = page;
      loadData();
    };

    // 自动刷新控制
    const toggleAutoRefresh = (enabled) => {
      if (enabled) {
        refreshTimer.value = setInterval(() => {
          loadData();
        }, 30000); // 30秒刷新一次
        ElMessage.success('已开启自动刷新（30秒间隔）');
      } else {
        if (refreshTimer.value) {
          clearInterval(refreshTimer.value);
          refreshTimer.value = null;
        }
        ElMessage.info('已关闭自动刷新');
      }
    };

    // 查看详情
    const viewDetails = async (record) => {
      currentFile.value = record;
      detailDrawerVisible.value = true;

      try {
        // 获取分析结果内容
        const response = await axios.get(`${apiBaseUrl}/api/files/${record.id}/analysis-content`);
        analysisContent.value = response.data.content || response.data.summary || '暂无详细内容';
      } catch (error) {
        console.error('获取分析结果内容失败:', error);
        ElMessage.error('获取分析结果内容失败');
        analysisContent.value = '无法加载分析结果内容';
      }
    };

    // 下载分析报告
    const downloadAnalysis = async (file) => {
      try {
        const response = await axios.get(`${apiBaseUrl}/api/files/${file.id}/download_result`, {
          responseType: 'blob'
        });

        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${file.filename}_分析报告.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('报告下载成功');
      } catch (error) {
        console.error('下载报告失败:', error);
        ElMessage.error('下载报告失败，请稍后重试');
      }
    };

    // 标记为已处理
    const markAsHandled = async (file) => {
      try {
        await ElMessageBox.confirm(
          `确定要将文件 "${file.filename}" 标记为已处理吗？`,
          '确认操作',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        // 这里可以调用后端API标记文件状态
        // await axios.put(`${apiBaseUrl}/api/files/${file.id}/mark-handled`);

        ElMessage.success('已标记为已处理');
        loadData(); // 重新加载数据
      } catch (error) {
        if (error !== 'cancel') {
          console.error('标记处理状态失败:', error);
          ElMessage.error('操作失败，请稍后重试');
        }
      }
    };

    // 上报风险
    const escalateRisk = async (file) => {
      try {
        await ElMessageBox.confirm(
          `确定要上报文件 "${file.filename}" 的风险吗？这将通知相关负责人。`,
          '风险上报',
          {
            confirmButtonText: '确定上报',
            cancelButtonText: '取消',
            type: 'error',
          }
        );

        // 这里可以调用后端API上报风险
        // await axios.post(`${apiBaseUrl}/api/files/${file.id}/escalate`);

        ElMessage.success('风险已上报，相关人员将收到通知');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('上报风险失败:', error);
          ElMessage.error('上报失败，请稍后重试');
        }
      }
    };

    // 导出数据
    const exportData = () => {
      try {
        const csvContent = generateCSVContent();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `危险情感文件报告_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('报告导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        ElMessage.error('导出失败，请稍后重试');
      }
    };

    // 生成CSV内容
    const generateCSVContent = () => {
      const headers = ['文件名', '上传时间', '风险等级', '危险情感', '文件大小'];
      const rows = filteredFileList.value.map(file => [
        file.filename,
        file.upload_time,
        getRiskLevel(file),
        detectDangerousEmotions(file.analysis_result).join(';'),
        formatFileSize(file.filesize)
      ]);

      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      return '\uFEFF' + csvContent; // 添加BOM以支持中文
    };

    // 获取建议
    const getSuggestionTitle = (file) => {
      const level = getRiskLevel(file);
      return level === '高风险' ? '紧急处理建议' :
             level === '中风险' ? '重点关注建议' : '一般关注建议';
    };

    const getSuggestionType = (file) => {
      const level = getRiskLevel(file);
      return level === '高风险' ? 'error' :
             level === '中风险' ? 'warning' : 'info';
    };

    const getSuggestionDescription = (file) => {
      const level = getRiskLevel(file);
      const emotions = detectDangerousEmotions(file.analysis_result);

      if (level === '高风险') {
        return `检测到高风险情感：${emotions.join('、')}。建议立即联系专业人员进行干预，并密切关注相关人员的心理状态。`;
      } else if (level === '中风险') {
        return `检测到中等风险情感：${emotions.join('、')}。建议加强关注，适时提供心理疏导和支持。`;
      } else {
        return `检测到轻微负面情感：${emotions.join('、')}。建议保持关注，提供必要的支持和帮助。`;
      }
    };

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B';

      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let index = 0;
      let formattedSize = size;

      while (formattedSize >= 1024 && index < units.length - 1) {
        formattedSize /= 1024;
        index++;
      }

      return `${formattedSize.toFixed(2)} ${units[index]}`;
    };

    // 检测危险情感
    const detectDangerousEmotions = (content) => {
      if (!content) return [];

      // 在内容中检测危险情感关键词
      return dangerousEmotions.filter(emotion => {
        return content.includes(emotion);
      });
    };

    // 组件挂载和卸载
    onMounted(() => {
      loadData();
    });

    onUnmounted(() => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
      }
    });

    return {
      // 响应式数据
      fileList,
      loading,
      detailDrawerVisible,
      currentFile,
      analysisContent,
      searchKeyword,
      riskLevelFilter,
      emotionTypeFilter,
      viewMode,
      autoRefresh,
      pagination,

      // 计算属性
      totalDangerousFiles,
      highRiskCount,
      mediumRiskCount,
      uniqueDangerousEmotions,
      allDangerousEmotions,
      filteredFileList,

      // 方法
      refreshData,
      handleSearch,
      handleFilter,
      handleSortChange,
      handleSizeChange,
      handleCurrentChange,
      toggleAutoRefresh,
      viewDetails,
      downloadAnalysis,
      markAsHandled,
      escalateRisk,
      exportData,
      getRiskLevel,
      getRiskLevelType,
      getRiskLevelEffect,
      getRiskLevelClass,
      getEmotionDescription,
      getSuggestionTitle,
      getSuggestionType,
      getSuggestionDescription,
      formatFileSize,
      detectDangerousEmotions,
    };
  },
});
</script>

<style scoped>
.emotion-warning {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #f56c6c;
  font-size: 32px;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计概览 */
.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.stat-card.high {
  background: linear-gradient(135deg, #ff8a80, #ff5722);
  color: white;
}

.stat-card.medium {
  background: linear-gradient(135deg, #ffb74d, #ff9800);
  color: white;
}

.stat-card.emotions {
  background: linear-gradient(135deg, #64b5f6, #2196f3);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
}

/* 表格区域 */
.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
  font-size: 16px;
}

.emotion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.emotion-tag {
  margin: 2px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 卡片视图 */
.card-view {
  margin-bottom: 24px;
}

.file-card {
  margin-bottom: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.file-card.high-risk {
  border-color: #f56c6c;
}

.file-card.medium-risk {
  border-color: #e6a23c;
}

.file-card.low-risk {
  border-color: #67c23a;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.filename {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-content {
  padding: 16px 0 0 0;
}

.file-meta {
  margin-bottom: 16px;
}

.file-meta p {
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.emotions-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 分页 */
.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* 详情抽屉 */
.detail-content {
  padding: 0;
}

.info-card,
.emotions-card,
.analysis-card,
.suggestions-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.emotions-analysis {
  padding: 16px 0;
}

.emotions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.emotion-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.emotion-description {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.analysis-content {
  padding: 16px 0;
}

.analysis-text {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.suggestions-content {
  padding: 16px 0;
}

.suggestion-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .emotion-warning {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .action-section {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .filter-left {
    width: 100%;
    flex-wrap: wrap;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .overview-section :deep(.el-col) {
    margin-bottom: 16px;
  }

  .card-view :deep(.el-col) {
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .stat-value {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .card-actions {
    flex-direction: column;
    gap: 8px;
  }

  .suggestion-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .emotion-warning {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .stat-content {
    padding: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
    margin-right: 12px;
  }

  .emotions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
