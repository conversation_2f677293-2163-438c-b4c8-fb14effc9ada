// 用户管理页面样式

.user-manage-wrapper {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .title {
        font-size: 22px;
        font-weight: 600;
      }
    }
    
    .right {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-filter {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .filter-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 20px;
    }
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .el-form-item {
        margin-bottom: 0;
        min-width: 250px;
      }
    }
    
    .filter-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .user-table {
    flex-grow: 1;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .el-card__header {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .title {
          font-size: 16px;
          font-weight: 500;
        }
        
        .right {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
    
    .el-card__body {
      padding: 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }
    
    .table-container {
      flex-grow: 1;
      overflow: auto;
      
      .el-table {
        .avatar-column {
          .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        
        .status-tag {
          &.active {
            background-color: #67c23a;
          }
          
          &.inactive {
            background-color: #909399;
          }
          
          &.locked {
            background-color: #f56c6c;
          }
        }
        
        .role-column {
          .admin {
            color: #409EFF;
            font-weight: 500;
          }
        }
        
        .action-column {
          .el-button {
            padding: 5px 8px;
            margin-left: 5px;
          }
        }
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: flex-end;
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
  
  .user-form-dialog {
    .el-form {
      .avatar-upload {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
        
        .avatar-preview {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          overflow: hidden;
          margin-bottom: 15px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
      
      .el-form-item {
        margin-bottom: 22px;
      }
    }
    
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .user-detail-drawer {
    .user-header {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      
      .avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 20px;
      }
      
      .user-info {
        .username {
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 5px;
        }
        
        .user-role {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          margin-right: 10px;
          
          &.admin {
            background-color: #409EFF;
            color: #fff;
          }
          
          &.user {
            background-color: #67c23a;
            color: #fff;
          }
        }
        
        .user-status {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          
          &.active {
            background-color: #67c23a;
            color: #fff;
          }
          
          &.inactive {
            background-color: #909399;
            color: #fff;
          }
          
          &.locked {
            background-color: #f56c6c;
            color: #fff;
          }
        }
      }
    }
    
    .detail-content {
      .info-section {
        margin-bottom: 30px;
        
        .section-title {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .info-list {
          .info-item {
            display: flex;
            margin-bottom: 15px;
            
            .label {
              width: 120px;
              color: #606266;
            }
            
            .value {
              flex: 1;
              color: #303133;
            }
          }
        }
      }
      
      .login-history {
        .history-item {
          padding: 12px 0;
          border-bottom: 1px solid #ebeef5;
          
          .login-time {
            margin-bottom: 5px;
            font-weight: 500;
          }
          
          .login-ip {
            color: #606266;
            font-size: 14px;
          }
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
    
    .action-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 30px;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
} 