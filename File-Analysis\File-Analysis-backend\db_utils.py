import pymysql
from config import DB_CONFIG
import datetime

def get_db_connection():
    """
    创建数据库连接
    :return: 数据库连接对象
    """
    try:
        connection = pymysql.connect(
            **DB_CONFIG,
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except pymysql.Error as e:
        print(f"数据库连接错误: {e}")
        return None

def execute_query(sql, params=None):
    """
    执行SQL查询
    :param sql: SQL语句
    :param params: SQL参数
    :return: 查询结果
    """
    connection = get_db_connection()
    if connection:
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params or ())
                result = cursor.fetchall()
                return result
        except pymysql.Error as e:
            print(f"查询执行错误: {e}")
            return None
        finally:
            connection.close()
    return None

def execute_insert(sql, params=None):
    """
    执行SQL插入操作
    :param sql: SQL语句
    :param params: SQL参数
    :return: (是否成功, 插入ID或错误信息)
    """
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params or ())
            connection.commit()
            return True, cursor.lastrowid
    except pymysql.Error as e:
        return False, f"数据库错误: {str(e)}"
    finally:
        connection.close()

def execute_update(sql, params=None):
    """
    执行SQL更新操作
    :param sql: SQL语句
    :param params: SQL参数
    :return: (是否成功, 影响行数或错误信息)
    """
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params or ())
            connection.commit()
            return True, cursor.rowcount
    except pymysql.Error as e:
        return False, f"数据库错误: {str(e)}"
    finally:
        connection.close()

def format_datetime(item, date_fields=None):
    """
    格式化日期时间字段
    :param item: 数据字典
    :param date_fields: 日期字段列表
    :return: 格式化后的数据字典
    """
    if not date_fields:
        date_fields = ['created_at', 'updated_at', 'last_login', 'upload_time']
    
    for field in date_fields:
        if field in item and isinstance(item[field], (datetime.datetime, datetime.date)):
            item[field] = item[field].isoformat()
    
    return item

def format_items_datetime(items, date_fields=None):
    """
    批量格式化数据项的日期时间字段
    :param items: 数据项列表
    :param date_fields: 日期字段列表
    :return: 格式化后的数据项列表
    """
    if not items:
        return items
    
    for item in items:
        format_datetime(item, date_fields)
    
    return items

def get_paginated_data(table, page=1, page_size=10, where_clause="", params=None, order_by="id DESC"):
    """
    通用分页查询函数
    :param table: 表名
    :param page: 页码
    :param page_size: 每页条数
    :param where_clause: WHERE子句（不含"WHERE"关键字）
    :param params: SQL参数
    :param order_by: 排序条件
    :return: 查询结果和分页信息
    """
    if params is None:
        params = []
    
    # 构建WHERE子句
    where_sql = f"WHERE {where_clause}" if where_clause else ""
    
    # 计算总数
    count_sql = f"SELECT COUNT(*) as total FROM {table} {where_sql}"
    count_result = execute_query(count_sql, params)
    total = count_result[0]['total'] if count_result else 0
    
    # 分页查询
    offset = (page - 1) * page_size
    sql = f"""
    SELECT * FROM {table} 
    {where_sql}
    ORDER BY {order_by}
    LIMIT %s OFFSET %s
    """
    
    # 添加分页参数
    query_params = params + [page_size, offset]
    
    result = execute_query(sql, query_params)
    
    # 格式化日期时间
    result = format_items_datetime(result)
    
    return {
        'items': result or [],
        'total': total,
        'page': page,
        'page_size': page_size
    }

if __name__ == '__main__':
    # 测试连接
    conn = get_db_connection()
    if conn:
        print("数据库连接成功！")
        conn.close()
    else:
        print("数据库连接失败！") 