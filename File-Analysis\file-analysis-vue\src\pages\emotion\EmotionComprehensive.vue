<template>
  <div class="emotion-comprehensive">
    <div class="page-header">
      <h1>情感综合分析</h1>
      <div class="page-description">
        展示最近100条分析结果中出现的各种情感及其频率
      </div>
    </div>

    <div class="chart-container">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>情感分布统计</span>
            <el-button type="primary" @click="refreshData">刷新数据</el-button>
          </div>
        </template>
        <div class="chart-wrapper">
          <!-- 安全情感与危险情感分布对比图 -->
          <div id="emotion-comparison-chart" class="chart"></div>
          
          <el-divider content-position="center">详细分布</el-divider>
          
          <div class="charts-row">
            <!-- 安全情感饼图 -->
            <div id="safe-emotions-pie" class="chart half-width"></div>
            
            <!-- 危险情感饼图 -->
            <div id="dangerous-emotions-pie" class="chart half-width"></div>
          </div>
          
          <el-divider content-position="center">排行榜</el-divider>
          
          <div class="charts-row">
            <!-- 安全情感柱状图 -->
            <div id="safe-emotions-bar" class="chart half-width"></div>
            
            <!-- 危险情感柱状图 -->
            <div id="dangerous-emotions-bar" class="chart half-width"></div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import { apiBaseUrl } from '@/config';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { PieChart, BarChart } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  BarChart,
  LabelLayout,
  CanvasRenderer
]);

export default defineComponent({
  name: 'EmotionComprehensive',
  setup() {
    // 存储图表实例
    let charts = {};
    // 数据
    let emotionData = {
      safe: [],
      dangerous: []
    };
    
    // 安全情感颜色
    const safeColors = [
      '#91cc75', '#73c0de', '#5470c6', '#91d5ff', '#b7eb8f',
      '#87e8de', '#9254de', '#36cfc9', '#40a9ff', '#52c41a'
    ];
    
    // 危险情感颜色
    const dangerousColors = [
      '#ee6666', '#fac858', '#fc8452', '#ff7a45', '#ff4d4f',
      '#ffa940', '#ff9c6e', '#ff7875', '#ffbb96', '#ffccc7'
    ];

    // 获取情感统计数据
    const fetchEmotionStatistics = async () => {
      try {
        const response = await axios.get(`${apiBaseUrl}/api/emotion-statistics`, {
          params: { limit: 100 }
        });
        
        emotionData.safe = response.data.safe_emotions;
        emotionData.dangerous = response.data.dangerous_emotions;
        
        // 初始化图表
        initCharts();
        
      } catch (error) {
        console.error('获取情感统计数据失败:', error);
        ElMessage.error('获取统计数据失败，请稍后重试');
      }
    };

    // 初始化图表
    const initCharts = () => {
      // 先销毁已有的图表实例
      Object.values(charts).forEach(chart => {
        chart && chart.dispose();
      });
      
      // 创建对比图
      charts.comparison = initComparisonChart();
      
      // 创建饼图
      charts.safePie = initPieChart(
        'safe-emotions-pie',
        emotionData.safe,
        '安全情感分布',
        safeColors
      );
      
      charts.dangerousPie = initPieChart(
        'dangerous-emotions-pie',
        emotionData.dangerous,
        '危险情感分布',
        dangerousColors
      );
      
      // 创建柱状图
      charts.safeBar = initBarChart(
        'safe-emotions-bar',
        emotionData.safe.slice(0, 10),
        '安全情感Top10',
        '#91cc75'
      );
      
      charts.dangerousBar = initBarChart(
        'dangerous-emotions-bar',
        emotionData.dangerous.slice(0, 10),
        '危险情感Top10',
        '#ee6666'
      );
    };

    // 初始化对比图
    const initComparisonChart = () => {
      // 计算安全情感和危险情感的总数
      const safeTotal = emotionData.safe.reduce((sum, item) => sum + item.value, 0);
      const dangerousTotal = emotionData.dangerous.reduce((sum, item) => sum + item.value, 0);
      const total = safeTotal + dangerousTotal;
      
      const chart = echarts.init(document.getElementById('emotion-comparison-chart'));
      
      const option = {
        title: {
          text: '情感整体分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['安全情感', '危险情感']
        },
        series: [
          {
            name: '情感分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: safeTotal, name: '安全情感', itemStyle: { color: '#91cc75' } },
              { value: dangerousTotal, name: '危险情感', itemStyle: { color: '#ee6666' } }
            ]
          }
        ]
      };
      
      chart.setOption(option);
      return chart;
    };

    // 初始化饼图
    const initPieChart = (elementId, data, title, colorList) => {
      if (data.length === 0) {
        return null;
      }
      
      const chart = echarts.init(document.getElementById(elementId));
      
      const option = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          type: 'scroll',
          pageIconSize: 12,
          pageTextStyle: {
            color: '#888'
          },
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '情感频次',
            type: 'pie',
            radius: '60%',
            center: ['55%', '50%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            color: colorList
          }
        ]
      };
      
      chart.setOption(option);
      return chart;
    };

    // 初始化柱状图
    const initBarChart = (elementId, data, title, color) => {
      if (data.length === 0) {
        return null;
      }
      
      const chart = echarts.init(document.getElementById(elementId));
      
      // 反转数据顺序以便从高到低显示
      const sortedData = [...data].sort((a, b) => a.value - b.value);
      
      const option = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: sortedData.map(item => item.name),
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        series: [
          {
            name: '出现次数',
            type: 'bar',
            data: sortedData.map(item => item.value),
            itemStyle: {
              color: color
            }
          }
        ]
      };
      
      chart.setOption(option);
      return chart;
    };

    // 刷新数据
    const refreshData = () => {
      fetchEmotionStatistics();
      ElMessage.success('数据已刷新');
    };

    // 窗口大小改变时重绘图表
    const resizeCharts = () => {
      Object.values(charts).forEach(chart => {
        chart && chart.resize();
      });
    };

    onMounted(() => {
      fetchEmotionStatistics();
      window.addEventListener('resize', resizeCharts);
    });

    onUnmounted(() => {
      // 清除图表实例
      Object.values(charts).forEach(chart => {
        chart && chart.dispose();
      });
      window.removeEventListener('resize', resizeCharts);
    });

    return {
      refreshData
    };
  },
});
</script>

<style scoped>
.emotion-comprehensive {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-description {
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 16px;
}

.chart-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-wrapper {
  padding: 10px;
}

.chart {
  height: 400px;
  margin-bottom: 20px;
}

.charts-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.half-width {
  width: calc(50% - 10px);
  height: 350px;
}

@media (max-width: 768px) {
  .half-width {
    width: 100%;
  }
}
</style>
