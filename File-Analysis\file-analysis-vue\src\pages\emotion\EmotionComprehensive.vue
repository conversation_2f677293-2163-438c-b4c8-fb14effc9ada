<template>
  <div class="emotion-comprehensive">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">情感综合分析</h1>
          <p class="page-description">
            基于最近 {{ dataLimit }} 条分析结果的情感统计与可视化展示
          </p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Refresh" @click="refreshData" :loading="loading">
            刷新数据
          </el-button>
          <el-select v-model="dataLimit" @change="handleLimitChange" style="width: 120px; margin-left: 12px;">
            <el-option label="50条" :value="50" />
            <el-option label="100条" :value="100" />
            <el-option label="200条" :value="200" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon safe">
                <el-icon><Sunny /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">安全情感</div>
                <div class="card-value">{{ safeEmotionsTotal }}</div>
                <div class="card-subtitle">{{ safeEmotionsCount }} 种类型</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon danger">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">危险情感</div>
                <div class="card-value">{{ dangerousEmotionsTotal }}</div>
                <div class="card-subtitle">{{ dangerousEmotionsCount }} 种类型</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">总计分析</div>
                <div class="card-value">{{ totalEmotions }}</div>
                <div class="card-subtitle">情感出现次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon ratio">
                <el-icon><PieChart /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">安全比例</div>
                <div class="card-value">{{ safeRatio }}%</div>
                <div class="card-subtitle">安全情感占比</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要图表区域 -->
    <div class="main-charts">
      <el-row :gutter="16">
        <!-- 整体分布饼图 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">情感整体分布</span>
                <el-tag :type="safeEmotionsTotal > dangerousEmotionsTotal ? 'success' : 'danger'">
                  {{ safeEmotionsTotal > dangerousEmotionsTotal ? '安全为主' : '需要关注' }}
                </el-tag>
              </div>
            </template>
            <div id="emotion-overview-chart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 情感趋势图 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">Top10 情感排行</span>
                <el-switch
                  v-model="showDangerousOnly"
                  active-text="仅危险"
                  inactive-text="全部"
                  @change="updateTopChart"
                />
              </div>
            </template>
            <div id="emotion-top-chart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析区域 -->
    <div class="detail-charts">
      <el-row :gutter="16">
        <!-- 安全情感详细分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">安全情感分布</span>
                <el-tag type="success">{{ safeEmotionsCount }} 种</el-tag>
              </div>
            </template>
            <div id="safe-emotions-chart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 危险情感详细分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">危险情感分布</span>
                <el-tag type="danger">{{ dangerousEmotionsCount }} 种</el-tag>
              </div>
            </template>
            <div id="dangerous-emotions-chart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">详细数据</span>
            <div class="table-actions">
              <el-radio-group v-model="tableView" @change="handleTableViewChange">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="safe">安全情感</el-radio-button>
                <el-radio-button label="dangerous">危险情感</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <el-table :data="currentTableData" stripe style="width: 100%" max-height="400">
          <el-table-column prop="name" label="情感名称" width="200">
            <template #default="{ row }">
              <el-tag :type="getEmotionType(row.name)" size="small">
                {{ row.name }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="出现次数" width="120" sortable>
            <template #default="{ row }">
              <el-text type="primary" size="large">{{ row.value }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="占比" width="120">
            <template #default="{ row }">
              {{ ((row.value / totalEmotions) * 100).toFixed(1) }}%
            </template>
          </el-table-column>
          <el-table-column label="可视化" width="200">
            <template #default="{ row }">
              <el-progress
                :percentage="(row.value / maxEmotionValue) * 100"
                :color="getEmotionType(row.name) === 'danger' ? '#f56c6c' : '#67c23a'"
                :show-text="false"
              />
            </template>
          </el-table-column>
          <el-table-column label="分类" width="100">
            <template #default="{ row }">
              <el-tag :type="getEmotionType(row.name)" size="small">
                {{ getEmotionType(row.name) === 'danger' ? '危险' : '安全' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, onMounted, onUnmounted, ref, computed } from 'vue';
import axios from 'axios';
import { apiBaseUrl } from '@/config';
import { ElMessage } from 'element-plus';
import { Refresh, Sunny, Warning, DataAnalysis, PieChart } from '@element-plus/icons-vue';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { PieChart as EchartsPieChart, BarChart } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  EchartsPieChart,
  BarChart,
  LabelLayout,
  CanvasRenderer
]);

export default defineComponent({
  name: 'EmotionComprehensive',
  components: {
    Refresh,
    Sunny,
    Warning,
    DataAnalysis,
    PieChart
  },
  setup() {
    // 响应式数据
    const loading = ref(false);
    const dataLimit = ref(100);
    const showDangerousOnly = ref(false);
    const tableView = ref('all');

    // 存储图表实例
    let charts = {};

    // 情感数据
    const emotionData = ref({
      safe: [],
      dangerous: []
    });

    // 安全情感列表（用于判断情感类型）
    const safeEmotionsList = [
      '积极', '希望', '乐观', '信心', '感恩', '信任', '挑战', '怀旧',
      '平静', '喜悦', '共情', '宽恕', '敬畏', '满足', '勇气', '好奇', '奉献'
    ];

    // 颜色配置
    const safeColors = [
      '#67c23a', '#85ce61', '#95d475', '#a4da89', '#b3e19d',
      '#c2e7b0', '#d1edc4', '#e1f3d8', '#409eff', '#66b1ff'
    ];

    const dangerousColors = [
      '#f56c6c', '#f78989', '#f9a6a6', '#fbc4c4', '#fcd3d3',
      '#fde2e2', '#fef0f0', '#ff7875', '#ff9c6e', '#ffbb96'
    ];

    // 计算属性
    const safeEmotionsTotal = computed(() => {
      return emotionData.value.safe.reduce((sum, item) => sum + item.value, 0);
    });

    const dangerousEmotionsTotal = computed(() => {
      return emotionData.value.dangerous.reduce((sum, item) => sum + item.value, 0);
    });

    const totalEmotions = computed(() => {
      return safeEmotionsTotal.value + dangerousEmotionsTotal.value;
    });

    const safeEmotionsCount = computed(() => {
      return emotionData.value.safe.length;
    });

    const dangerousEmotionsCount = computed(() => {
      return emotionData.value.dangerous.length;
    });

    const safeRatio = computed(() => {
      if (totalEmotions.value === 0) return 0;
      return Math.round((safeEmotionsTotal.value / totalEmotions.value) * 100);
    });

    const maxEmotionValue = computed(() => {
      const allEmotions = [...emotionData.value.safe, ...emotionData.value.dangerous];
      return allEmotions.length > 0 ? Math.max(...allEmotions.map(item => item.value)) : 0;
    });

    const currentTableData = computed(() => {
      let data = [];
      if (tableView.value === 'all') {
        data = [...emotionData.value.safe, ...emotionData.value.dangerous];
      } else if (tableView.value === 'safe') {
        data = emotionData.value.safe;
      } else {
        data = emotionData.value.dangerous;
      }
      return data.sort((a, b) => b.value - a.value);
    });

    // 获取情感统计数据
    const fetchEmotionStatistics = async () => {
      loading.value = true;
      try {
        const response = await axios.get(`${apiBaseUrl}/api/emotion-statistics`, {
          params: { limit: dataLimit.value }
        });

        emotionData.value.safe = response.data.safe_emotions || [];
        emotionData.value.dangerous = response.data.dangerous_emotions || [];

        // 初始化图表
        await initCharts();

      } catch (error) {
        console.error('获取情感统计数据失败:', error);
        ElMessage.error('获取统计数据失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 工具方法
    const getEmotionType = (emotionName) => {
      return safeEmotionsList.includes(emotionName) ? 'success' : 'danger';
    };

    const handleLimitChange = () => {
      fetchEmotionStatistics();
    };

    const handleTableViewChange = () => {
      // 表格视图切换时的处理逻辑
    };

    const updateTopChart = () => {
      if (charts.topChart) {
        initTopChart();
      }
    };

    // 初始化图表
    const initCharts = async () => {
      // 先销毁已有的图表实例
      Object.values(charts).forEach(chart => {
        chart && chart.dispose();
      });
      charts = {};

      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 创建各种图表
      initOverviewChart();
      initTopChart();
      initSafeEmotionsChart();
      initDangerousEmotionsChart();
    };

    // 初始化整体分布图
    const initOverviewChart = () => {
      const element = document.getElementById('emotion-overview-chart');
      if (!element) return;

      const chart = echarts.init(element);
      charts.overview = chart;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['安全情感', '危险情感']
        },
        series: [
          {
            name: '情感分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}\n{c}次\n({d}%)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: [
              {
                value: safeEmotionsTotal.value,
                name: '安全情感',
                itemStyle: { color: '#67c23a' }
              },
              {
                value: dangerousEmotionsTotal.value,
                name: '危险情感',
                itemStyle: { color: '#f56c6c' }
              }
            ]
          }
        ]
      };

      chart.setOption(option);
    };

    // 初始化Top10排行图
    const initTopChart = () => {
      const element = document.getElementById('emotion-top-chart');
      if (!element) return;

      const chart = echarts.init(element);
      charts.topChart = chart;

      let data = [];
      if (showDangerousOnly.value) {
        data = emotionData.value.dangerous.slice(0, 10);
      } else {
        const allEmotions = [...emotionData.value.safe, ...emotionData.value.dangerous];
        data = allEmotions.sort((a, b) => b.value - a.value).slice(0, 10);
      }

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: data.map(item => item.name).reverse(),
          axisLabel: {
            interval: 0
          }
        },
        series: [
          {
            name: '出现次数',
            type: 'bar',
            data: data.map(item => ({
              value: item.value,
              itemStyle: {
                color: safeEmotionsList.includes(item.name) ? '#67c23a' : '#f56c6c'
              }
            })).reverse(),
            barWidth: '60%'
          }
        ]
      };

      chart.setOption(option);
    };

    // 初始化安全情感图表
    const initSafeEmotionsChart = () => {
      const element = document.getElementById('safe-emotions-chart');
      if (!element || emotionData.value.safe.length === 0) return;

      const chart = echarts.init(element);
      charts.safeChart = chart;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          type: 'scroll',
          pageIconSize: 10,
          data: emotionData.value.safe.map(item => item.name)
        },
        series: [
          {
            name: '安全情感',
            type: 'pie',
            radius: '65%',
            center: ['60%', '50%'],
            data: emotionData.value.safe,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            color: safeColors
          }
        ]
      };

      chart.setOption(option);
    };

    // 初始化危险情感图表
    const initDangerousEmotionsChart = () => {
      const element = document.getElementById('dangerous-emotions-chart');
      if (!element || emotionData.value.dangerous.length === 0) return;

      const chart = echarts.init(element);
      charts.dangerousChart = chart;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          type: 'scroll',
          pageIconSize: 10,
          data: emotionData.value.dangerous.map(item => item.name)
        },
        series: [
          {
            name: '危险情感',
            type: 'pie',
            radius: '65%',
            center: ['60%', '50%'],
            data: emotionData.value.dangerous,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            color: dangerousColors
          }
        ]
      };

      chart.setOption(option);
    };

    // 刷新数据
    const refreshData = () => {
      fetchEmotionStatistics();
      ElMessage.success('数据已刷新');
    };

    // 窗口大小改变时重绘图表
    const resizeCharts = () => {
      Object.values(charts).forEach(chart => {
        chart && chart.resize();
      });
    };

    onMounted(() => {
      fetchEmotionStatistics();
      window.addEventListener('resize', resizeCharts);
    });

    onUnmounted(() => {
      // 清除图表实例
      Object.values(charts).forEach(chart => {
        chart && chart.dispose();
      });
      window.removeEventListener('resize', resizeCharts);
    });

    return {
      // 响应式数据
      loading,
      dataLimit,
      showDangerousOnly,
      tableView,

      // 计算属性
      safeEmotionsTotal,
      dangerousEmotionsTotal,
      totalEmotions,
      safeEmotionsCount,
      dangerousEmotionsCount,
      safeRatio,
      maxEmotionValue,
      currentTableData,

      // 方法
      refreshData,
      getEmotionType,
      handleLimitChange,
      handleTableViewChange,
      updateTopChart
    };
  },
});
</script>

<style scoped>
.emotion-comprehensive {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.action-section {
  display: flex;
  align-items: center;
}

/* 概览卡片 */
.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  height: 120px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.card-icon.safe {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.card-icon.danger {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.card-icon.ratio {
  background: linear-gradient(135deg, #9254de, #b37feb);
  color: white;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

/* 图表区域 */
.main-charts,
.detail-charts {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-container {
  height: 400px;
  padding: 16px;
}

/* 数据表格 */
.data-table {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .emotion-comprehensive {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .action-section {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .overview-cards :deep(.el-col) {
    margin-bottom: 16px;
  }

  .main-charts :deep(.el-col),
  .detail-charts :deep(.el-col) {
    margin-bottom: 16px;
  }

  .chart-container {
    height: 300px;
  }

  .page-title {
    font-size: 24px;
  }

  .card-value {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .emotion-comprehensive {
    padding: 12px;
  }

  .page-header {
    padding: 12px;
  }

  .card-content {
    padding: 12px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
    margin-right: 12px;
  }

  .chart-container {
    height: 250px;
    padding: 8px;
  }
}
</style>
