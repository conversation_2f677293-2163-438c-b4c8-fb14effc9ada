import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
  timeout: 60000 // 请求超时时间
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在请求发送之前做一些处理
    
    // 从localStorage获取用户ID，如果存在则添加到请求头
    const userId = localStorage.getItem('userId')
    if (userId) {
      config.headers['X-User-Id'] = userId
      console.log('添加用户ID到请求头:', userId)
    } else {
      console.log('未找到用户ID，请先登录')
    }
    
    return config
  },
  error => {
    // 处理请求错误
    console.log(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    // 处理错误响应
    let message = '请求失败'
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      message = (data && data.error) || `请求错误 (${status})`
      
      // 处理401未授权错误
      if (status === 401) {
        // 可以在这里处理登录过期的逻辑
        localStorage.removeItem('userId')
        localStorage.removeItem('username')
        localStorage.removeItem('email')
        localStorage.removeItem('phone')
        localStorage.removeItem('role')
        
        // 如果不是登录页面，则重定向到登录页
        if (window.location.pathname !== '/login') {
          ElMessage.error('登录已过期，请重新登录')
          setTimeout(() => {
            window.location.href = '/login'
          }, 1500)
        }
      }
    } else if (error.request) {
      // 请求发送成功，但没有收到响应
      message = '服务器无响应'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export { request }
export default request 