import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import { ElMessage } from 'element-plus'

const routes = [
  {
    path: '/',
    component: MainLayout,
    redirect: '/login',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/pages/Dashboard.vue'),
        meta: { title: '首页', requiresAuth: true }
      },
      {
        path: '/emotion/analysis',
        name: 'EmotionAnalysis',
        component: () => import('@/pages/emotion/EmotionAnalysis.vue'),
        meta: { title: '情绪分析', requiresAuth: true }
      },
      {
        path: '/emotion/warning',
        name: 'EmotionWarning',
        component: () => import('@/pages/emotion/EmotionWarning.vue'),
        meta: { title: '情绪预警', requiresAuth: true }
      },
      {
        path: '/emotion/comprehensive',
        name: 'EmotionComprehensive',
        component: () => import('@/pages/emotion/EmotionComprehensive.vue'),
        meta: { title: '综合分析', requiresAuth: true }
      },
      {
        path: '/user/user',
        name: 'UserManage',
        component: () => import('@/pages/user/UserManage.vue'),
        meta: { title: '用户管理', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: '/user/profile',
        name: 'UserProfile',
        component: () => import('@/pages/UserProfile.vue'),
        meta: { title: '个人资料', requiresAuth: true }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/pages/Register.vue'),
    meta: { title: '注册' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 后台管理系统` : '后台管理系统'
  
  // 检查该路由是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查用户是否已登录
    const isLoggedIn = !!localStorage.getItem('userId')
    if (!isLoggedIn) {
      // 未登录则跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原本想访问的路径，登录成功后跳转回去
      })
    } else {
      // 检查路由是否需要管理员权限
      if (to.meta.requiresAdmin) {
        const role = Number(localStorage.getItem('role') || 1)
        if (role !== 0) {
          ElMessage.warning('您没有权限访问该页面')
          next('/dashboard')
          return
        }
      }
      next() // 已登录，正常跳转
    }
  } else {
    // 如果用户已登录且尝试访问登录页，则重定向到首页
    if ((to.path === '/login' || to.path === '/register') && !!localStorage.getItem('userId')) {
      next('/dashboard')
    } else {
      next() // 不需要登录权限的页面，正常跳转
    }
  }
})

export default router 