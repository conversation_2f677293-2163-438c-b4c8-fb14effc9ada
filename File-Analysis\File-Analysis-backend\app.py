from flask import Flask, render_template, jsonify, request, send_file
from flask_cors import CORS
from up_document import UploadDocument
from config import UPLOAD_CONFIG, REPORTS_CONFIG
from file_db import get_files, get_file_by_id, get_files_paginated, update_analysis_result_file, get_dangerous_emotion_files, get_emotion_statistics
from users_db import register_user, login_user, get_user_by_id, update_user_profile, change_password, get_all_users, create_user, update_user, delete_user
from db_utils import execute_query, execute_update
import os
import time
import re
import docx  # 添加python-docx库用于读取Word文档

app = Flask(__name__)
# 增强CORS配置，允许所有来源、方法和头部
CORS(app, resources={r"/*": {"origins": "*", "methods": ["GET", "POST", "DELETE", "OPTIONS", "PUT"], "allow_headers": "*"}})

# 设置最大文件大小
app.config['MAX_CONTENT_LENGTH'] = UPLOAD_CONFIG['MAX_CONTENT_LENGTH']

# 用户注册路由
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()
    
    # 验证请求数据
    if not data or 'username' not in data or 'password' not in data or 'email' not in data:
        return jsonify({"error": "用户名、密码和邮箱是必填项"}), 400
    
    username = data['username']
    password = data['password']
    email = data['email']
    phone = data.get('phone')  # 手机号是可选的
    
    # 基本验证
    if not username or not password or not email:
        return jsonify({"error": "用户名、密码和邮箱不能为空"}), 400
    
    if len(password) < 6:
        return jsonify({"error": "密码长度不能小于6位"}), 400
    
    # 调用注册函数
    success, result = register_user(username, password, email, phone)
    
    if success:
        return jsonify({"message": "注册成功", "user_id": result}), 201
    else:
        return jsonify({"error": result}), 400

# 用户登录路由
@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    
    # 验证请求数据
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({"error": "用户名/邮箱和密码是必填项"}), 400
    
    username = data['username']  # 可以是用户名或邮箱
    password = data['password']
    
    # 调用登录函数
    success, result = login_user(username, password)
    
    if success:
        # 登录成功，返回用户信息
        return jsonify({"message": "登录成功", "user": result}), 200
    else:
        # 登录失败，返回错误信息
        return jsonify({"error": result}), 401

# 获取用户信息路由
@app.route('/api/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    user_data = get_user_by_id(user_id)
    
    if user_data:
        return jsonify(user_data), 200
    else:
        return jsonify({"error": "用户不存在"}), 404

# 更新用户个人资料
@app.route('/api/users/<int:user_id>/profile', methods=['PUT'])
def update_profile(user_id):
    data = request.get_json()
    
    if not data:
        return jsonify({"error": "请提供要更新的资料"}), 400
    
    email = data.get('email')
    phone = data.get('phone')
    
    success, result = update_user_profile(user_id, email, phone)
    
    if success:
        return jsonify({"message": result}), 200
    else:
        return jsonify({"error": result}), 400

# 修改用户密码
@app.route('/api/users/<int:user_id>/password', methods=['PUT'])
def update_password(user_id):
    data = request.get_json()
    
    if not data or 'old_password' not in data or 'new_password' not in data:
        return jsonify({"error": "旧密码和新密码是必填项"}), 400
    
    old_password = data['old_password']
    new_password = data['new_password']
    
    # 基本验证
    if len(new_password) < 6:
        return jsonify({"error": "新密码长度不能小于6位"}), 400
    
    success, result = change_password(user_id, old_password, new_password)
    
    if success:
        return jsonify({"message": result}), 200
    else:
        return jsonify({"error": result}), 400

# ===== 用户管理 API =====

# 获取所有用户列表（管理员功能）
@app.route('/api/admin/users', methods=['GET'])
def get_users():
    # 检查是否是管理员
    user_id = request.headers.get('X-User-Id')
    if not user_id:
        return jsonify({"error": "未授权，请先登录"}), 401
    
    admin_check = get_user_by_id(int(user_id))
    if not admin_check or admin_check.get('role') != 0:
        return jsonify({"error": "权限不足，仅管理员可访问"}), 403
    
    # 获取查询参数
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 10))
    
    # 获取用户列表
    users = get_all_users(page, page_size)
    return jsonify(users), 200

# 创建用户（管理员功能）
@app.route('/api/admin/users', methods=['POST'])
def create_user_admin():
    # 检查是否是管理员
    user_id = request.headers.get('X-User-Id')
    if not user_id:
        return jsonify({"error": "未授权，请先登录"}), 401
    
    admin_check = get_user_by_id(int(user_id))
    if not admin_check or admin_check.get('role') != 0:
        return jsonify({"error": "权限不足，仅管理员可访问"}), 403
    
    data = request.get_json()
    
    # 验证请求数据
    if not data or 'username' not in data or 'password' not in data or 'email' not in data:
        return jsonify({"error": "用户名、密码和邮箱是必填项"}), 400
    
    username = data['username']
    password = data['password']
    email = data['email']
    phone = data.get('phone')
    role = data.get('role', 1)  # 默认为普通用户
    status = data.get('status', 1)  # 默认为启用状态
    
    # 创建用户
    success, result = create_user(username, password, email, phone, role, status)
    
    if success:
        return jsonify({"message": "用户创建成功", "user_id": result}), 201
    else:
        return jsonify({"error": result}), 400

# 更新用户信息（管理员功能）
@app.route('/api/admin/users/<int:target_user_id>', methods=['PUT'])
def update_user_admin(target_user_id):
    # 检查是否是管理员
    user_id = request.headers.get('X-User-Id')
    if not user_id:
        return jsonify({"error": "未授权，请先登录"}), 401
    
    admin_check = get_user_by_id(int(user_id))
    if not admin_check or admin_check.get('role') != 0:
        return jsonify({"error": "权限不足，仅管理员可访问"}), 403
    
    data = request.get_json()
    
    if not data:
        return jsonify({"error": "请提供要更新的信息"}), 400
    
    # 更新用户信息
    success, result = update_user(target_user_id, data)
    
    if success:
        return jsonify({"message": result}), 200
    else:
        return jsonify({"error": result}), 400

# 删除用户（管理员功能）
@app.route('/api/admin/users/<int:target_user_id>', methods=['DELETE'])
def delete_user_admin(target_user_id):
    # 检查是否是管理员
    user_id = request.headers.get('X-User-Id')
    if not user_id:
        return jsonify({"error": "未授权，请先登录"}), 401
    
    admin_check = get_user_by_id(int(user_id))
    if not admin_check or admin_check.get('role') != 0:
        return jsonify({"error": "权限不足，仅管理员可访问"}), 403
    
    # 防止管理员删除自己
    if int(user_id) == target_user_id:
        return jsonify({"error": "不能删除当前登录的管理员账户"}), 400
    
    # 删除用户
    success, result = delete_user(target_user_id)
    
    if success:
        return jsonify({"message": result}), 200
    else:
        return jsonify({"error": result}), 400

@app.route('/api/upload', methods=['POST'])
def upload_file():
    print("收到上传请求")
    if 'file' not in request.files:
        print("没有文件在请求中")
        return jsonify({"error": "没有文件"}), 400
    
    file = request.files['file']
    print(f"文件名: {file.filename}, 文件类型: {file.content_type}")
    
    uploader = UploadDocument()
    success, result = uploader.save_file(file)
    
    if success:
        print("文件上传成功")
        return jsonify(result)
    else:
        print(f"文件上传失败: {result}")
        return jsonify({"error": result}), 500

@app.route('/api/files', methods=['GET'])
def get_files_route():
    """获取文件列表（支持分页和筛选）"""
    # 获取查询参数
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 10))
    sort_field = request.args.get('sort_field', 'id')
    sort_order = request.args.get('sort_order', 'desc')
    status_filter = request.args.get('status', None)
    size_operator = request.args.get('size_operator', None)
    size_value = request.args.get('size_value', None)
    name_filter = request.args.get('name', None)
    
    # 处理日期范围
    date_range = None
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    if start_date and end_date:
        date_range = [start_date, end_date]
    
    # 使用分页查询获取数据
    result = get_files_paginated(
        page=page,
        page_size=page_size,
        sort_field=sort_field,
        sort_order=sort_order,
        status_filter=status_filter,
        size_operator=size_operator,
        size_value=size_value,
        name_filter=name_filter,
        date_range=date_range
    )
    
    return jsonify(result)

@app.route('/api/files/<int:file_id>', methods=['GET'])
def get_file_detail(file_id):
    file_data = get_file_by_id(file_id)
    
    if file_data:
        # 确保前端可以获取唯一文件名
        if 'unique_filename' in file_data:
            file_data['unique_filename'] = file_data['unique_filename']
        
        return jsonify(file_data)
    else:
        return jsonify({"error": "文件不存在"}), 404

@app.route('/api/files/<int:file_id>/analyze', methods=['POST'])
def analyze_file(file_id):
    """解析文件"""
    file_data = get_file_by_id(file_id)
    
    if not file_data:
        return jsonify({"error": "文件不存在"}), 404
    
    # 更新文件状态为"解析中"
    sql_update = "UPDATE uploaded_files SET status = '解析中' WHERE id = %s"
    execute_update(sql_update, (file_id,))
    
    try:
        # 获取文件的实际路径
        original_filename = file_data['unique_filename']
        file_path = os.path.join(UPLOAD_CONFIG['UPLOAD_FOLDER'], original_filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({"error": "文件不存在或已被删除"}), 404
        
        # 检查文件是否为WAV文件（.wav后缀或_wav后缀）
        if original_filename.lower().endswith('.wav') or original_filename.lower().endswith('_wav'):
            # 异步处理文件解析
            import threading
            def process_file():
                try:
                    # 导入语音转文本和情感分析模块
                    from speech_to_text import process_audio_text_analysis, convert_summary_to_word, convert_analysis_to_word
                    # 导入文件数据库工具
                    from file_db import update_analysis_result_file
                    
                    # 调用音频处理流程，包含语音转文本、情感分析和二次总结
                    # 设置summary_only=True以仅转换总结结果为Word
                    success, text_content, analysis_result, summary_result, word_file_path = process_audio_text_analysis(file_path, convert_to_word=True, summary_only=True)
                    
                    if success:
                        # 使用配置中的reports文件夹路径
                        reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
                        
                        # 从总结结果中提取情感特征文本
                        from speech_to_text import extract_emotion_result
                        emotion_text = extract_emotion_result(summary_result)
                        
                        # 如果成功提取到情感特征文本，则先更新数据库
                        if emotion_text:
                            # 无论文件保存成功与否，都先将情感分析结果保存到数据库
                            update_emotion_sql = """
                            UPDATE uploaded_files 
                            SET analysis_result = %s
                            WHERE id = %s
                            """
                            execute_update(update_emotion_sql, (emotion_text, file_id))
                            print(f"已更新文件 {file_id} 的情感分析结果: {emotion_text}")
                        
                        if word_file_path:
                            # 如果已经生成了Word文件，直接使用
                            word_filename = os.path.basename(word_file_path)
                            # 更新数据库中的result_file字段为文件名
                            update_file_sql = """
                            UPDATE uploaded_files 
                            SET result_file = %s, status = '解析成功'
                            WHERE id = %s
                            """
                            file_save_success, _ = execute_update(update_file_sql, (word_filename, file_id))
                            
                            if file_save_success:
                                print(f"文件 {original_filename} 解析成功，结果已保存到Word文件: {word_filename}")
                            else:
                                print(f"文件 {original_filename} 解析成功，但保存结果文件名失败")
                        else:
                            # 如果没有生成Word文件，才执行额外的Markdown和Word转换流程
                            # 生成一个基础文件名
                            base_filename = f"analysis_report_{file_id}_{int(time.time())}"
                            
                            # 保存为Markdown文件
                            from speech_to_text import save_analysis_to_markdown, md_to_word
                            md_success, md_file_path = save_analysis_to_markdown(
                                text_content, 
                                analysis_result, 
                                summary_result,
                                base_filename
                            )
                            
                            if md_success:
                                print(f"已成功保存为Markdown文件: {md_file_path}")
                                
                                # 将Markdown文件转换为Word文档
                                word_success, word_file_path = md_to_word(md_file_path, '')
                                
                                if word_success:
                                    # 获取Word文件名（不含路径）
                                    word_filename = os.path.basename(word_file_path)
                                    
                                    # 更新数据库中的result_file字段为文件名
                                    update_file_sql = """
                                    UPDATE uploaded_files 
                                    SET result_file = %s, status = '解析成功'
                                    WHERE id = %s
                                    """
                                    file_save_success, _ = execute_update(update_file_sql, (word_filename, file_id))
                                    
                                    if file_save_success:
                                        print(f"文件 {original_filename} 解析成功，结果已保存到Word文件: {word_filename}")
                                    else:
                                        print(f"文件 {original_filename} 解析成功，但保存结果文件名失败")
                                else:
                                    # Word转换失败，将分析结果直接保存到数据库
                                    print(f"转换Word文档失败: {word_file_path}，将直接保存分析结果")
                                    # 尝试使用Markdown文件作为分析结果
                                    md_filename = os.path.basename(md_file_path)
                                    update_file_sql = """
                                    UPDATE uploaded_files 
                                    SET result_file = %s, status = '解析成功'
                                    WHERE id = %s
                                    """
                                    execute_update(update_file_sql, (md_filename, file_id))
                            else:
                                # 保存Markdown失败，将分析结果直接保存到数据库
                                print(f"保存为Markdown文件失败: {md_file_path}，将直接保存结果")
                                if emotion_text:
                                    update_sql = """
                                    UPDATE uploaded_files 
                                    SET status = '解析成功'
                                    WHERE id = %s
                                    """
                                    execute_update(update_sql, (file_id,))
                    else:
                        # 解析失败，更新状态
                        error_message = text_content or analysis_result or summary_result or "未知错误"
                        update_sql = """
                        UPDATE uploaded_files 
                        SET status = '解析失败', analysis_result = %s
                        WHERE id = %s
                        """
                        execute_update(update_sql, (f"解析错误: {error_message}", file_id))
                        print(f"文件 {original_filename} 解析失败：{error_message}")
                    
                    # 解析完成后，检查是否为*_wav格式文件
                    if original_filename.endswith('_wav') and not original_filename.endswith('.wav'):
                        # 保留临时wav文件，删除原始*_wav文件
                        wav_temp_file = file_path + '.wav'
                        if os.path.exists(wav_temp_file):
                            # 只有在临时文件存在的情况下才删除原始文件
                            try:
                                # 更新数据库中的文件名
                                update_filename_sql = """
                                UPDATE uploaded_files 
                                SET unique_filename = %s
                                WHERE id = %s
                                """
                                new_unique_filename = original_filename + '.wav'
                                execute_update(update_filename_sql, (new_unique_filename, file_id))
                                print(f"已更新数据库中的文件名为: {new_unique_filename}")
                                
                                # 删除原始文件
                                if os.path.exists(file_path):
                                    os.remove(file_path)
                                    print(f"已删除原始*_wav文件: {file_path}")
                            except Exception as e:
                                print(f"删除原始文件时出错: {str(e)}")
                    
                except Exception as e:
                    # 解析失败，更新状态
                    error_message = str(e)
                    update_sql = """
                    UPDATE uploaded_files 
                    SET status = '解析失败', analysis_result = %s
                    WHERE id = %s
                    """
                    execute_update(update_sql, (f"解析错误: {error_message}", file_id))
                    print(f"文件 {original_filename} 解析失败：{error_message}")
            
            # 启动异步线程处理
            thread = threading.Thread(target=process_file)
            thread.daemon = True
            thread.start()
            
            return jsonify({"message": "文件解析请求已提交，正在处理中"}), 200
        else:
            # 不是WAV文件，更新状态为解析失败
            update_sql = """
            UPDATE uploaded_files 
            SET status = '解析失败', analysis_result = %s
            WHERE id = %s
            """
            execute_update(update_sql, ("不支持的文件类型，只能解析WAV音频文件", file_id))
            return jsonify({"error": "不支持的文件类型，只能解析WAV音频文件"}), 400
            
    except Exception as e:
        # 发生异常，更新状态为解析失败
        error_message = str(e)
        update_sql = """
        UPDATE uploaded_files 
        SET status = '解析失败', analysis_result = %s
        WHERE id = %s
        """
        execute_update(update_sql, (f"解析错误: {error_message}", file_id))
        return jsonify({"error": f"解析过程发生错误: {error_message}"}), 500

@app.route('/api/files/<int:file_id>', methods=['DELETE'])
def delete_file(file_id):
    """删除文件"""
    file_data = get_file_by_id(file_id)
    
    if not file_data:
        return jsonify({"error": "文件不存在"}), 404
    
    try:
        # 删除物理文件
        file_path = os.path.join(UPLOAD_CONFIG['UPLOAD_FOLDER'], file_data['unique_filename'])
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"已删除物理文件: {file_path}")
            
        # 检查并删除可能存在的临时WAV文件
        wav_file_path = file_path + '.wav'
        if os.path.exists(wav_file_path):
            os.remove(wav_file_path)
            print(f"已删除临时WAV文件: {wav_file_path}")
        
        # 检查并删除可能存在的分析结果文件
        # 使用result_file字段获取文件名
        result_file_name = file_data.get('result_file')
        if result_file_name:
            # 使用配置中的reports文件夹路径
            reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
            result_file_path = os.path.join(reports_dir, result_file_name)
            if os.path.exists(result_file_path):
                os.remove(result_file_path)
                print(f"已删除分析结果文件: {result_file_path}")
        
        # 删除数据库记录
        sql = "DELETE FROM uploaded_files WHERE id = %s"
        execute_update(sql, (file_id,))
        print(f"已删除数据库记录，ID: {file_id}")
        
        return jsonify({"message": "文件已删除"}), 200
        
    except Exception as e:
        error_message = str(e)
        print(f"删除文件时发生错误: {error_message}")
        return jsonify({"error": f"删除文件时发生错误: {error_message}"}), 500

@app.route('/api/files/<int:file_id>/download_result', methods=['GET'])
def download_analysis_result(file_id):
    """下载分析结果文件"""
    file_data = get_file_by_id(file_id)
    
    if not file_data:
        return jsonify({"error": "文件不存在"}), 404
    
    if not file_data.get('status') or file_data.get('status') != '解析成功':
        return jsonify({"error": "文件未成功解析"}), 404
    
    # 使用result_file字段获取文件名
    result_file_name = file_data.get('result_file')
    if not result_file_name:
        return jsonify({"error": "分析结果文件不存在或未设置"}), 404
    
    try:
        # 使用配置中的reports文件夹路径
        reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        # 构建结果文件的完整路径
        result_file_path = os.path.join(reports_dir, result_file_name)
        
        # 打印路径用于调试
        print(f"尝试下载文件: {result_file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(result_file_path):
            return jsonify({"error": f"分析结果文件不存在或已被删除: {result_file_path}"}), 404
        
        # 获取文件扩展名以确定MIME类型
        file_ext = os.path.splitext(result_file_name)[1].lower()
        mime_type = 'text/plain'  # 默认为文本文件
        
        # 根据扩展名确定MIME类型
        if file_ext == '.pdf':
            mime_type = 'application/pdf'
        elif file_ext == '.docx':
            mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif file_ext == '.doc':
            mime_type = 'application/msword'
        elif file_ext == '.xlsx':
            mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif file_ext == '.xls':
            mime_type = 'application/vnd.ms-excel'
        
        # 设置用户友好的下载文件名
        download_name = f"分析报告_{file_data['filename']}{file_ext}"
        
        # 返回文件进行下载
        return send_file(
            result_file_path,
            mimetype=mime_type,
            as_attachment=True,  # 作为附件下载
            download_name=download_name  # 自定义下载文件名
        )
        
    except Exception as e:
        error_message = str(e)
        print(f"下载分析结果文件时发生错误: {error_message}")
        return jsonify({"error": f"下载分析结果文件时发生错误: {error_message}"}), 500

@app.route('/api/files/<int:file_id>/analysis_summary', methods=['GET'])
def get_analysis_summary(file_id):
    """获取分析报告中的情感摘要"""
    file_data = get_file_by_id(file_id)
    
    if not file_data:
        return jsonify({"error": "文件不存在"}), 404
    
    if file_data.get('status') != '解析成功':
        return jsonify({"error": "文件未成功解析"}), 404
    
    try:
        # 直接从数据库中获取情感特征文本
        analysis_result = file_data.get('analysis_result', '')
        
        # 如果analysis_result为空，可能是旧数据，尝试从文件中读取
        if not analysis_result and file_data.get('result_file'):
            # 使用配置中的reports文件夹路径
            reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
            result_file_path = os.path.join(reports_dir, file_data['result_file'])
            
            # 检查文件是否存在且是docx文件
            if os.path.exists(result_file_path) and result_file_path.lower().endswith('.docx'):
                try:
                    # 打开Word文档
                    doc = docx.Document(result_file_path)
                    
                    # 搜索包含情感特征的段落
                    for para in doc.paragraphs:
                        text = para.text.strip()
                        if "经分析，当前主体情感具有" in text:
                            analysis_result = text
                            break
                    
                    # 如果找到情感特征，更新数据库
                    if analysis_result:
                        update_sql = """
                        UPDATE uploaded_files 
                        SET analysis_result = %s
                        WHERE id = %s
                        """
                        execute_update(update_sql, (analysis_result, file_id))
                except Exception as e:
                    print(f"读取Word文档提取情感时出错: {str(e)}")
        
        # 使用正则表达式提取情感词汇
        import re
        emotions = []
        # 匹配"经分析，当前主体情感具有（xxx）的心理特征"模式中的情感词汇
        pattern = r"经分析，当前主体情感具有[（(]([^）)]+)[）)]"
        match = re.search(pattern, analysis_result or '')
        
        if match:
            # 提取括号中的情感词汇，分成列表
            emotions_str = match.group(1)
            emotions = [e.strip() for e in re.split(r'[,，、]', emotions_str) if e.strip()]
        
        # 如果没有找到情感词，返回默认消息
        if not emotions:
            return jsonify({
                "summary": analysis_result or "分析成功，可下载查看完整结果",
                "emotions": []
            })
        
        # 返回情感摘要和情感词列表
        return jsonify({
            "summary": analysis_result,
            "emotions": emotions
        })
        
    except Exception as e:
        error_message = str(e)
        print(f"获取分析摘要时发生错误: {error_message}")
        return jsonify({"error": f"获取分析摘要时发生错误: {error_message}"}), 500

@app.route('/api/files/dangerous-emotions', methods=['GET'])
def get_dangerous_emotions_files():
    """获取包含危险情感的文件列表（支持分页）"""
    # 获取查询参数
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 10))
    sort_field = request.args.get('sort_field', 'id')
    sort_order = request.args.get('sort_order', 'desc')
    
    # 调用file_db中的函数获取危险情感文件
    results = get_dangerous_emotion_files(page, page_size, sort_field, sort_order)
    
    return jsonify(results), 200

@app.route('/api/files/<int:file_id>/analysis-content', methods=['GET'])
def get_analysis_content(file_id):
    """获取文件的分析结果内容"""
    try:
        # 获取文件信息
        file_data = get_file_by_id(file_id)
        if not file_data:
            return jsonify({"error": "文件不存在"}), 404
        
        # 检查分析结果是否存在
        if not file_data.get('analysis_result'):
            return jsonify({"error": "该文件尚未分析或分析结果不可用"}), 404
        
        result_filename = file_data.get('analysis_result')
        reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        result_file_path = os.path.join(reports_dir, result_filename)
        
        # 检查文件是否存在
        if not os.path.exists(result_file_path):
            return jsonify({"error": "分析结果文件不存在"}), 404
        
        # 读取分析结果内容
        content = ""
        file_ext = os.path.splitext(result_file_path)[1].lower()
        
        if file_ext == '.docx':
            # 处理Word文档
            doc = docx.Document(result_file_path)
            content = "\n".join([para.text for para in doc.paragraphs])
        else:
            # 处理文本文件
            try:
                with open(result_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试其他编码
                with open(result_file_path, 'r', encoding='gbk') as f:
                    content = f.read()
        
        return jsonify({"content": content}), 200
        
    except Exception as e:
        error_message = str(e)
        return jsonify({"error": f"获取分析结果内容时发生错误: {error_message}"}), 500

@app.route('/api/emotion-statistics', methods=['GET'])
def get_emotion_stats():
    """获取情感统计数据"""
    try:
        # 获取参数
        limit = int(request.args.get('limit', 100))
        
        # 调用file_db中的函数
        statistics = get_emotion_statistics(limit)
        
        return jsonify(statistics), 200
    except Exception as e:
        error_message = str(e)
        return jsonify({"error": f"获取情感统计数据时发生错误: {error_message}"}), 500

if __name__ == '__main__':
    # 确保必要的目录存在
    try:
        # 检查上传目录是否存在
        upload_dir = UPLOAD_CONFIG['UPLOAD_FOLDER']
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)
            print(f"创建上传目录: {upload_dir}")
        
        # 检查报告目录是否存在
        reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
            print(f"创建报告目录: {reports_dir}")
            
        # 数据库迁移：将analysis_result中的文件名移动到result_file字段
        try:
            import sqlite3
            from config import DB_CONFIG
            
            # 连接数据库
            db_path = DB_CONFIG.get('DATABASE', 'file_analysis.db')
            print(f"连接数据库: {db_path}")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找所有解析成功且analysis_result中可能包含文件名的记录
            cursor.execute("SELECT id, analysis_result FROM uploaded_files WHERE status='解析成功'")
            records = cursor.fetchall()
            
            update_count = 0
            for record in records:
                file_id, analysis_result = record
                # 如果analysis_result看起来像文件名（包含扩展名）
                if analysis_result and '.' in analysis_result and not analysis_result.startswith('经分析'):
                    # 将文件名移动到result_file字段
                    cursor.execute(
                        "UPDATE uploaded_files SET result_file = ?, analysis_result = NULL WHERE id = ?", 
                        (analysis_result, file_id)
                    )
                    update_count += 1
            
            conn.commit()
            print(f"数据迁移完成: 已将 {update_count} 条记录的文件名从analysis_result移动到result_file")
            
            conn.close()
        except Exception as e:
            print(f"数据库迁移出错: {str(e)}")
            
        print("系统目录检查完毕")
    except Exception as e:
        print(f"检查系统目录时出错: {str(e)}")
    
    # 启动服务
    print("正在启动文件分析系统服务...")
    app.run(debug=True, host='0.0.0.0', port=5000)
