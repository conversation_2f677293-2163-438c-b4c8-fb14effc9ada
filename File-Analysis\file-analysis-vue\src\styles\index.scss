/* 导入变量和基础样式 */
@use './variables.scss';
@use './app.scss';

/* 布局样式 */
@use './layouts/mainLayout.scss';

/* 页面样式 */
@use './pages/dashboard.scss';
@use './pages/login.scss';
@use './pages/fileManage.scss';
@use './pages/fileUpload.scss';

/* 组件样式 */
@use './components/fileUploadDialog.scss';

/* 全局基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: var(--font-size-base);
  color: var(--text-color-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f5f7fa;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

// 自定义变量
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --text-color: #333;
  --text-color-secondary: #909399;
  --border-color: #dcdfe6;
  --background-color: #f5f7fa;
  --sidebar-width: 220px;
  --header-height: 60px;
}

// 通用样式
.app-container {
  padding: 20px;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
} 