<script setup>
// App 根组件
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { StagewiseToolbar } from '@stagewise/toolbar-vue';
import { VuePlugin } from '@stagewise-plugins/vue';

const isDev = ref(import.meta.env.DEV);
const router = useRouter();
const userStore = useUserStore();

// 在应用挂载时检查登录状态
onMounted(() => {
  // 如果用户未登录且当前不在登录或注册页面，则重定向到登录页
  const currentPath = window.location.pathname;
  if (!userStore.isLoggedIn && 
      currentPath !== '/login' && 
      currentPath !== '/register') {
    router.push('/login');
  }
});

// stagewise 工具栏配置
const stagewiseConfig = {
  plugins: [VuePlugin]
};
</script>

<template>
  <router-view />
  <StagewiseToolbar v-if="isDev" :config="stagewiseConfig" />
</template>
