<template>
  <div class="file-list-container">
    <el-card class="box-card" shadow="hover" :body-style="{ padding: '0' }">
      <div class="filter-container">
        <div class="filter-row first-row">
          <div class="filter-group sort-group">
            <span class="filter-label">排序:</span>
            <div class="sort-selects">
              <el-radio-group v-model="sortField" size="small" @change="handleSortConfirm">
                <el-radio label="id">序号</el-radio>
                <el-radio label="upload_time">时间</el-radio>
                <el-radio label="filesize">大小</el-radio>
                <el-radio label="filename">名称</el-radio>
              </el-radio-group>
              <el-radio-group v-model="sortOrder" size="small" @change="handleSortConfirm">
                <el-radio label="desc">降序</el-radio>
                <el-radio label="asc">升序</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-group name-group">
            <span class="filter-label">名称:</span>
            <el-input
              v-model="nameFilter"
              size="small"
              placeholder="请输入文件名"
              class="filter-input name-input"
            />
          </div>
          
          <div class="filter-group date-group">
            <span class="filter-label">开始:</span>
            <el-date-picker
              v-model="startDate"
              type="date"
              size="small"
              placeholder="开始日期"
              value-format="YYYY-MM-DD"
              :disabledDate="disabledDate"
              class="filter-input date-input"
              :style="{ width: '100%' }"
            />
          </div>
          
          <div class="filter-group date-group">
            <span class="filter-label">结束:</span>
            <el-date-picker
              v-model="endDate"
              type="date"
              size="small"
              placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :disabledDate="disabledDate"
              class="filter-input date-input"
              :style="{ width: '100%' }"
            />
          </div>
          
          <div class="filter-actions first-row-actions">
            <el-button type="success" size="small" @click="showUploadDialog">
              <el-icon><Upload /></el-icon>上传文件
            </el-button>
          </div>
        </div>
        
        <div class="filter-row second-row">
          <div class="filter-group size-group">
            <span class="filter-label">大小:</span>
            <div class="size-filter-container">
              <el-radio-group v-model="sizeOperator" size="small" @change="handleSizeOperatorChange">
                <el-radio label="全部">全部</el-radio>
                <el-radio label=">=">>=</el-radio>
                <el-radio label="<="><=</el-radio>
              </el-radio-group>
              
              <el-radio-group v-model="sizeValue" size="small" :disabled="sizeOperator === '全部'">
                <el-radio label="100" :disabled="sizeOperator === '全部'">100MB</el-radio>
                <el-radio label="500" :disabled="sizeOperator === '全部'">500MB</el-radio>
                <el-radio label="1024" :disabled="sizeOperator === '全部'">1GB</el-radio>
                <el-radio label="other" :disabled="sizeOperator !== '全部'">其他</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <div class="filter-group status-group">
            <span class="filter-label">状态:</span>
            <el-radio-group v-model="statusFilter" size="small">
              <el-radio label="全部">全部</el-radio>
              <el-radio label="未解析">未解析</el-radio>
              <el-radio label="解析中">解析中</el-radio>
              <el-radio label="解析成功">解析成功</el-radio>
              <el-radio label="解析失败">解析失败</el-radio>
            </el-radio-group>
          </div>
          
          <div class="filter-actions second-row-actions">
            <el-button type="primary" size="small" @click="handleSearch">
              <el-icon><Search /></el-icon>搜索
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 批量操作区 -->
      <div class="batch-actions" v-if="selectedFiles.length > 0">
        <div class="batch-info">
          <el-tag type="info" effect="plain">已选择 {{ selectedFiles.length }} 个文件</el-tag>
        </div>
        <div class="batch-buttons">
          <el-button 
            type="primary" 
            size="small" 
            @click="batchViewAnalysis" 
            :disabled="!canBatchView"
          >
            <el-icon><Document /></el-icon>批量查看报告
          </el-button>
          <el-button 
            type="success" 
            size="small" 
            @click="batchAnalyzeFiles" 
            :disabled="!canBatchAnalyze"
          >
            <el-icon><Connection /></el-icon>批量解析
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="batchDeleteFiles"
          >
            <el-icon><Delete /></el-icon>批量删除
          </el-button>
        </div>
      </div>
      
      <el-table 
        v-if="!useVirtualScroll"
        :data="fileList" 
        style="width: 100%" 
        v-loading="loading" 
        :cell-style="{ padding: '8px' }"
        :header-cell-style="{ 
          backgroundColor: '#f5f7fa', 
          color: '#606266', 
          fontWeight: 'bold',
          textAlign: 'center'
        }"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="序号" width="70" align="center" />
        <el-table-column prop="filename" label="文件名称" min-width="130" align="left">
          <template #default="scope">
            <div class="filename-cell">
              <el-icon><Document /></el-icon>
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="decodeFileName(scope.row.filename)"
                placement="top"
              >
                <span class="truncate-text">{{ decodeFileName(scope.row.filename) }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="unique_filename" label="唯一文件名" min-width="200" align="left">
          <template #default="scope">
            <div class="filename-cell">
              <el-icon><Key /></el-icon>
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="scope.row.unique_filename"
                placement="top"
              >
                <span class="truncate-text">{{ scope.row.unique_filename }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="filesize" label="文件大小" width="120" align="center">
          <template #default="scope">
            {{ formatFileSize(scope.row.filesize || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="210" align="center">
          <template #default="scope">
            <div class="status-container">
              <el-tag :type="getStatusType(scope.row.status)" size="small" effect="dark">
                {{ scope.row.status }}
              </el-tag>
              <div class="progress-wrapper">
                <el-progress 
                  :percentage="getAnalysisProgress(scope.row.id, scope.row.status)" 
                  :color="scope.row.status === '解析失败' ? '#f56c6c' : getProgressColor"
                  :stroke-width="6"
                  :show-text="false"
                  class="status-progress"
                  :class="{'is-failure': scope.row.status === '解析失败'}"
                />
                <span v-if="scope.row.status !== '解析失败'" class="progress-percentage">{{ getAnalysisProgress(scope.row.id, scope.row.status) }}%</span>
                <span v-else class="error-warning">
                  <el-icon><WarningFilled /></el-icon>
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="analysis_info" label="分析结论" min-width="200" align="left">
          <template #default="scope">
            <div v-if="scope.row.status === '解析成功' && scope.row.analysis_result">
              <span class="conclusion-text">{{ scope.row.analysis_result }}</span>
            </div>
            <span v-else-if="scope.row.status === '解析失败'" class="conclusion-failed">{{ scope.row.analysis_info || '解析失败' }}</span>
            <span v-else-if="scope.row.status === '解析中'" class="conclusion-processing">{{ scope.row.analysis_info || '处理中...' }}</span>
            <span v-else>{{ scope.row.analysis_info || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <div class="button-group">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewAnalysis(scope.row)"
                :disabled="scope.row.status === '解析中'"
              >
                {{ scope.row.status === '解析成功' ? '查看报告' : '解析' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteFile(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 虚拟滚动表格 -->
      <el-table
        v-else
        v-loading="loading"
        :data="fileList"
        style="width: 100%"
        :height="tableHeight"
        :cell-style="{ padding: '8px' }"
        :header-cell-style="{ 
          backgroundColor: '#f5f7fa', 
          color: '#606266', 
          fontWeight: 'bold',
          textAlign: 'center'
        }"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="序号" width="70" align="center" fixed />
        <el-table-column prop="filename" label="文件名称" width="150" align="left" fixed>
          <template #default="scope">
            <div class="filename-cell">
              <el-icon><Document /></el-icon>
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="decodeFileName(scope.row.filename)"
                placement="top"
              >
                <span class="truncate-text">{{ decodeFileName(scope.row.filename) }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="unique_filename" label="唯一文件名" width="200" align="left" fixed>
          <template #default="scope">
            <div class="filename-cell">
              <el-icon><Key /></el-icon>
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="scope.row.unique_filename"
                placement="top"
              >
                <span class="truncate-text">{{ scope.row.unique_filename }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="filesize" label="文件大小" width="100" align="center">
          <template #default="scope">
            {{ formatFileSize(scope.row.filesize || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="110" align="center">
          <template #default="scope">
            <div class="status-container">
              <el-tag :type="getStatusType(scope.row.status)" size="small" effect="dark">
                {{ scope.row.status }}
              </el-tag>
              <div class="progress-wrapper">
                <el-progress 
                  :percentage="getAnalysisProgress(scope.row.id, scope.row.status)" 
                  :color="scope.row.status === '解析失败' ? '#f56c6c' : getProgressColor"
                  :stroke-width="6"
                  :show-text="false"
                  class="status-progress"
                  :class="{'is-failure': scope.row.status === '解析失败'}"
                />
                <span v-if="scope.row.status !== '解析失败'" class="progress-percentage">{{ getAnalysisProgress(scope.row.id, scope.row.status) }}%</span>
                <span v-else class="error-warning">
                  <el-icon><WarningFilled /></el-icon>
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="analysis_info" label="分析结论" min-width="200" align="left">
          <template #default="scope">
            <div v-if="scope.row.status === '解析成功' && scope.row.analysis_result">
              <span class="conclusion-text">{{ scope.row.analysis_result }}</span>
            </div>
            <span v-else-if="scope.row.status === '解析失败'" class="conclusion-failed">{{ scope.row.analysis_info || '解析失败' }}</span>
            <span v-else-if="scope.row.status === '解析中'" class="conclusion-processing">{{ scope.row.analysis_info || '处理中...' }}</span>
            <span v-else>{{ scope.row.analysis_info || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <div class="button-group">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewAnalysis(scope.row)"
                :disabled="scope.row.status === '解析中'"
              >
                {{ scope.row.status === '解析成功' ? '查看报告' : '解析' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteFile(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :current-page="currentPage"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 文件上传对话框 -->
    <file-upload-dialog 
      v-model="uploadDialogVisible"
      @upload-success="handleUploadSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Search, Upload, Check, Key, WarningFilled, Delete, Connection, InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getFileList, deleteFile as deleteFileAPI, analyzeFile, getFileListPaginated, uploadFile, downloadAnalysisResult, getMimeType, getFileDetail } from '@/api/file'
import { request } from '@/utils/request'
import FileUploadDialog from '@/components/FileUploadDialog.vue'
import '@/styles/pages/fileManage.scss'

const router = useRouter()
const fileList = ref([])
const loading = ref(false)
const totalCount = ref(0)
const pageSize = ref(10)
const currentPage = ref(1)
const sortField = ref('id')
const sortOrder = ref('desc')
const dateRange = ref([])
const startDate = ref('')
const endDate = ref('')
const statusFilter = ref('全部')
const sizeOperator = ref('全部')
const sizeValue = ref('other')
const nameFilter = ref('')
const useVirtualScroll = ref(false)
const tableHeight = ref('calc(100vh - 260px)')
const uploadDialogVisible = ref(false)
const analysisProgressMap = ref({}) // 存储文件解析进度
const selectedFiles = ref([]) // 存储选中的文件
const pollingIntervals = ref({}) // 存储轮询定时器

// 计算属性：是否可以批量查看
const canBatchView = computed(() => {
  return selectedFiles.value.some(file => file.status === '解析成功')
})

// 计算属性：是否可以批量解析
const canBatchAnalyze = computed(() => {
  return selectedFiles.value.some(file => file.status === '未解析')
})

// 监听窗口大小变化，调整表格高度
const handleResize = () => {
  tableHeight.value = `calc(100vh - 260px)`
}

// 组件挂载时获取文件列表
onMounted(() => {
  fetchFileList()
  
  // 设置定时刷新，每60秒刷新一次列表
  const refreshInterval = setInterval(() => {
    fetchFileList()
  }, 60000)
  
  // 组件卸载时清除定时刷新
  onBeforeUnmount(() => {
    clearInterval(refreshInterval)
    // 清除所有文件状态轮询
    stopAllPolling()
  })
  
  // 初始化进度条 - 为所有文件创建进度，而不仅仅是"解析中"状态的文件
  setTimeout(() => {
    fileList.value.forEach(file => {
      if (!analysisProgressMap.value[file.id]) {
        // 根据状态设置不同的初始进度
        if (file.status === '解析中') {
          analysisProgressMap.value[file.id] = Math.floor(Math.random() * 40) + 30; // 30-70%的随机进度
        } else if (file.status === '解析成功') {
          analysisProgressMap.value[file.id] = 100; // 成功状态100%
        } else if (file.status === '解析失败') {
          analysisProgressMap.value[file.id] = 100; // 失败状态100%
        } else {
          analysisProgressMap.value[file.id] = 0; // 未解析状态0%
        }
      }
    })
  }, 300)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

// 获取查询参数
const getQueryParams = () => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    sort_field: sortField.value,
    sort_order: sortOrder.value
  }
  
  // 添加状态筛选
  if (statusFilter.value && statusFilter.value !== '全部') {
    params.status = statusFilter.value
  }
  
  // 添加大小筛选
  if (sizeOperator.value && sizeOperator.value !== '全部' && sizeValue.value && sizeValue.value !== 'other') {
    params.size_operator = sizeOperator.value
    params.size_value = sizeValue.value
  }
  
  // 添加名称筛选
  if (nameFilter.value) {
    params.name = nameFilter.value
  }
  
  // 添加日期筛选
  if (dateRange.value && dateRange.value.length === 2) {
    params.start_date = dateRange.value[0]
    params.end_date = dateRange.value[1]
  }
  
  return params
}

// 获取文件列表
const fetchFileList = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }
    
    // 添加状态筛选
    if (statusFilter.value !== '全部') {
      params.status = statusFilter.value
    }
    
    // 添加大小筛选
    if (sizeOperator.value !== '全部' && sizeValue.value !== 'other') {
      params.size_operator = sizeOperator.value
      params.size_value = sizeValue.value
    }
    
    // 添加名称筛选
    if (nameFilter.value) {
      params.name = nameFilter.value
    }
    
    // 添加日期范围
    if (startDate.value && endDate.value) {
      params.start_date = startDate.value
      params.end_date = endDate.value
    }
    
    // 使用API模块获取分页列表
    const response = await getFileListPaginated(params)
    fileList.value = response.items || []
    totalCount.value = response.total || 0
    
    // 检查并启动正在解析文件的轮询
    startPollingForProcessingFiles()
    
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败')
  } finally {
    loading.value = false
  }
}

// 启动对正在解析文件的轮询
const startPollingForProcessingFiles = () => {
  // 先清除所有现有的轮询
  stopAllPolling()
  
  // 找出所有正在解析的文件
  const processingFiles = fileList.value.filter(file => file.status.includes('解析中'))
  
  // 为每个正在解析的文件启动轮询
  processingFiles.forEach(file => {
    if (!pollingIntervals.value[file.id]) {
      // 设置轮询间隔为2秒
      pollingIntervals.value[file.id] = setInterval(() => {
        pollFileStatus(file.id)
      }, 2000)
      
      // 设置超时，最多轮询5分钟
      setTimeout(() => {
        if (pollingIntervals.value[file.id]) {
          clearInterval(pollingIntervals.value[file.id])
          delete pollingIntervals.value[file.id]
        }
      }, 5 * 60 * 1000)
    }
  })
}

// 轮询单个文件的状态
const pollFileStatus = async (fileId) => {
  try {
    // 使用getFileDetail代替getFileStatus
    const response = await getFileDetail(fileId)
    
    // 更新文件在列表中的状态
    const fileIndex = fileList.value.findIndex(file => file.id === fileId)
    if (fileIndex !== -1) {
      // 更新文件状态
      fileList.value[fileIndex].status = response.status
      
      // 更新进度 - 使用预定义的进度值
      if (response.status === '解析成功') {
        analysisProgressMap.value[fileId] = 100
      } else if (response.status === '解析失败') {
        analysisProgressMap.value[fileId] = 100
      } else if (response.status === '解析中') {
        // 如果是解析中状态，增加进度
        analysisProgressMap.value[fileId] = Math.min(95, (analysisProgressMap.value[fileId] || 10) + 5)
      }
      
      // 更新分析信息
      if (response.status === '解析成功') {
        fileList.value[fileIndex].analysis_info = '解析成功，可下载查看结果'
      } else if (response.status === '解析失败') {
        fileList.value[fileIndex].analysis_info = '解析失败，请检查文件内容'
        // 停止轮询
        stopPolling(fileId)
      } else if (response.status === '解析中') {
        fileList.value[fileIndex].analysis_info = '正在解析，请稍后刷新'
      }
      
      // 如果文件已完成解析，停止轮询
      if (response.status === '解析成功' || response.status === '解析失败') {
        stopPolling(fileId)
        
        // 如果解析成功，提示用户
        if (response.status === '解析成功') {
          ElMessage.success(`文件 ${decodeFileName(fileList.value[fileIndex].filename)} 解析完成`)
        }
      }
    }
  } catch (error) {
    console.error(`轮询文件 ${fileId} 状态失败:`, error)
  }
}

// 停止指定文件的轮询
const stopPolling = (fileId) => {
  if (pollingIntervals.value[fileId]) {
    clearInterval(pollingIntervals.value[fileId])
    delete pollingIntervals.value[fileId]
  }
}

// 停止所有轮询
const stopAllPolling = () => {
  Object.keys(pollingIntervals.value).forEach(fileId => {
    clearInterval(pollingIntervals.value[fileId])
  })
  pollingIntervals.value = {}
}

// 修改viewAnalysis函数，使用轮询机制
const viewAnalysis = (file) => {
  if (file.status === '解析成功') {
    // 文件已解析成功，直接下载分析结果
    if (file.analysis_result) {
      try {
        // 获取下载链接
        const downloadUrl = downloadAnalysisResult(file.id);
        console.log("下载链接:", downloadUrl);
        
        // 判断文件类型
        const fileExtension = file.analysis_result.split('.').pop().toLowerCase();
        const fileTypeMessage = fileExtension === 'docx' ? 'Word文档' : 
                               fileExtension === 'pdf' ? 'PDF文档' : '文本文件';
        
        // 创建临时链接进行下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        link.download = `分析报告_${decodeFileName(file.filename)}.${fileExtension}`;
        
        // 提示用户下载已开始
        ElMessage.success(`正在下载分析结果${fileTypeMessage}，请稍候...`);
        
        // 触发点击事件
        document.body.appendChild(link);
        link.click();
        
        // 移除临时元素
        setTimeout(() => {
          if (document.body.contains(link)) {
            document.body.removeChild(link);
          }
        }, 100);
      } catch (error) {
        console.error('下载文件失败:', error);
        ElMessage.error('下载文件失败，请稍后重试');
      }
    } else {
      ElMessage.warning('分析结果文件不存在');
    }
  } else if (file.status === '未解析') {
    ElMessageBox.confirm(
      `文件 ${decodeFileName(file.filename)} 尚未解析，是否立即解析？\n解析完成后将生成Word分析报告文档`,
      '提示',
      {
        confirmButtonText: '立即解析',
        cancelButtonText: '取消',
        type: 'info',
      }
    ).then(async () => {
      try {
        loading.value = true
        // 使用API模块发送解析请求
        await analyzeFile(file.id)
        ElMessage.success('解析请求已提交，系统正在后台处理，稍后可查看结果')
        
        // 初始化进度为10%
        analysisProgressMap.value[file.id] = 10
        
        // 开始轮询文件状态
        if (!pollingIntervals.value[file.id]) {
          pollingIntervals.value[file.id] = setInterval(() => {
            pollFileStatus(file.id)
          }, 2000)
          
          // 设置超时，最多轮询5分钟
          setTimeout(() => {
            stopPolling(file.id)
          }, 5 * 60 * 1000)
        }
        
        // 刷新列表以显示最新状态
        await fetchFileList()
      } catch (error) {
        console.error('提交解析请求失败:', error)
        ElMessage.error('提交解析请求失败')
      } finally {
        loading.value = false
      }
    }).catch(() => {})
  } else if (file.status === '解析中') {
    ElMessage.info('文件正在解析中，请稍后查看结果')
    
    // 确保正在轮询此文件的状态
    if (!pollingIntervals.value[file.id]) {
      pollingIntervals.value[file.id] = setInterval(() => {
        pollFileStatus(file.id)
      }, 2000)
      
      // 设置超时，最多轮询5分钟
      setTimeout(() => {
        stopPolling(file.id)
      }, 5 * 60 * 1000)
    }
  } else if (file.status === '解析失败') {
    // 显示失败原因
    ElMessageBox.alert(
      file.analysis_result || '未知错误',
      `文件 ${decodeFileName(file.filename)} 解析失败`,
      {
        confirmButtonText: '确定',
        type: 'error'
      }
    );
  }
}

// 删除文件
const handleDeleteFile = (file) => {
  ElMessageBox.confirm(
    `确定要删除文件 ${decodeFileName(file.filename)} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      loading.value = true
      // 使用API模块删除文件
      await deleteFileAPI(file.id)
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      // 删除成功后重新获取当前页数据
      fetchFileList()
    } catch (error) {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除操作，不做处理
  })
}

// 处理排序变化
const handleSortConfirm = () => {
  currentPage.value = 1 // 排序变化后重置为第一页
  fetchFileList()
}

// 处理搜索 - 点击搜索按钮时触发查询
const handleSearch = () => {
  currentPage.value = 1
  // 更新日期范围
  if (startDate.value && endDate.value) {
    dateRange.value = [startDate.value, endDate.value]
  } else {
    dateRange.value = []
  }
  fetchFileList()
}

// 显示上传对话框
const showUploadDialog = () => {
  uploadDialogVisible.value = true
}

// 处理上传成功
const handleUploadSuccess = () => {
  fetchFileList()
}

// 修改大小操作符变化处理函数，不再自动触发查询
const handleSizeOperatorChange = (value) => {
  if (value === '全部') {
    sizeValue.value = 'other'
  } else if (sizeValue.value === 'other') {
    sizeValue.value = '100'
  }
  // 不再调用 handleFilterChange()
}

// 禁用日期函数 - 不设置任何限制
const disabledDate = (time) => {
  return false; // 返回false表示不禁用任何日期
}

// 获取进度条颜色的方法
const getProgressColor = (percentage) => {
  if (percentage < 30) {
    return '#909399' // 灰色
  }
  if (percentage < 70) {
    return '#e6a23c' // 黄色
  }
  return '#67c23a' // 绿色
}

// 修改获取文件解析进度函数
const getAnalysisProgress = (fileId, status) => {
  // 如果没有设置进度或者文件不在进度映射表中，创建一个初始进度
  if (!analysisProgressMap.value[fileId]) {
    // 根据状态设置初始进度
    if (status === '解析中') {
      analysisProgressMap.value[fileId] = Math.floor(Math.random() * 40) + 30; // 30-70%的随机进度
    } else if (status === '解析成功') {
      analysisProgressMap.value[fileId] = 100; // 成功状态100%
    } else if (status === '未解析') {
      analysisProgressMap.value[fileId] = 0; // 未解析状态0%
    } else if (status === '解析失败') {
      analysisProgressMap.value[fileId] = 100; // 失败状态100%，但显示为红色
    }
  }
  
  // 返回当前进度
  return analysisProgressMap.value[fileId];
}

// 处理表格多选变化
const handleSelectionChange = (selection) => {
  selectedFiles.value = selection
  console.log('已选择文件:', selection)
}

// 批量查看报告
const batchViewAnalysis = () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要查看的文件')
    return
  }
  
  // 过滤出解析成功的文件
  const successFiles = selectedFiles.value.filter(file => file.status === '解析成功')
  
  if (successFiles.length === 0) {
    ElMessage.warning('所选文件中没有解析成功的文件')
    return
  }
  
  // 对每个文件执行下载操作
  successFiles.forEach(file => {
    try {
      // 获取下载链接
      const downloadUrl = downloadAnalysisResult(file.id)
      
      // 判断文件类型
      const fileExtension = file.analysis_result.split('.').pop().toLowerCase();
      
      // 使用a标签进行下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.target = '_blank';
      link.download = `分析报告_${decodeFileName(file.filename)}.${fileExtension}`;
      
      // 触发点击事件
      document.body.appendChild(link);
      link.click();
      
      // 延迟移除临时元素
      setTimeout(() => {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
      }, 100);
    } catch (error) {
      console.error(`下载文件 ${file.id} 失败:`, error)
      ElMessage.error(`文件 ${decodeFileName(file.filename)} 下载失败`)
    }
  })
  
  ElMessage.success(`正在下载 ${successFiles.length} 个文件的分析报告`)
}

// 批量解析文件
const batchAnalyzeFiles = () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要解析的文件')
    return
  }
  
  // 过滤出未解析的文件
  const unanalyzedFiles = selectedFiles.value.filter(file => file.status === '未解析')
  
  if (unanalyzedFiles.length === 0) {
    ElMessage.warning('所选文件中没有未解析的文件')
    return
  }
  
  ElMessageBox.confirm(
    `确定要解析已选择的 ${unanalyzedFiles.length} 个文件吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(async () => {
    try {
      loading.value = true
      
      // 记录解析请求成功和失败的数量
      let successCount = 0
      let failCount = 0
      
      // 依次对每个未解析的文件发起解析请求
      for (const file of unanalyzedFiles) {
        try {
          await analyzeFile(file.id)
          
          // 初始化解析进度
          if (!analysisProgressMap.value[file.id]) {
            analysisProgressMap.value[file.id] = 10 // 初始进度10%
          }
          
          successCount++
        } catch (error) {
          console.error(`解析文件 ${file.id} 失败:`, error)
          failCount++
        }
      }
      
      // 显示解析请求结果统计
      if (successCount > 0) {
        ElMessage.success(`成功提交 ${successCount} 个文件的解析请求`)
      }
      if (failCount > 0) {
        ElMessage.error(`${failCount} 个文件解析请求失败`)
      }
      
      // 解析操作完成后重新获取当前页数据
      fetchFileList()
      
      // 设置定时刷新，跟踪解析进度
      if (successCount > 0) {
        const checkInterval = setInterval(async () => {
          await fetchFileList()
          
          // 检查所有文件是否已经解析完成
          const allCompleted = unanalyzedFiles.every(file => {
            const updatedFile = fileList.value.find(f => f.id === file.id)
            return updatedFile && (updatedFile.status === '解析成功' || updatedFile.status === '解析失败')
          })
          
          // 更新解析进度 - 模拟进度增长
          unanalyzedFiles.forEach(file => {
            const updatedFile = fileList.value.find(f => f.id === file.id)
            if (updatedFile && updatedFile.status === '解析中') {
              if (!analysisProgressMap.value[file.id]) {
                analysisProgressMap.value[file.id] = 10 // 初始进度10%
              } else if (analysisProgressMap.value[file.id] < 90) {
                // 每次增加一些进度，最多到90%
                analysisProgressMap.value[file.id] += Math.floor(Math.random() * 5) + 1
              }
            }
          })
          
          if (allCompleted) {
            clearInterval(checkInterval)
            ElMessage.info('所有文件解析已完成')
          }
        }, 5000)
        
        // 设置一个超时时间，最多检查10分钟
        setTimeout(() => {
          clearInterval(checkInterval)
        }, 10 * 60 * 1000)
      }
    } catch (error) {
      console.error('批量解析文件失败:', error)
      ElMessage.error('批量解析文件失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消解析操作，不做处理
  })
}

// 批量删除文件
const batchDeleteFiles = () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除已选择的 ${selectedFiles.value.length} 个文件吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      loading.value = true
      
      // 记录删除成功和失败的数量，用于最终展示结果
      let successCount = 0
      let failCount = 0
      
      // 依次删除每个选中的文件
      for (const file of selectedFiles.value) {
        try {
          await deleteFileAPI(file.id)
          successCount++
        } catch (error) {
          console.error(`删除文件 ${file.id} 失败:`, error)
          failCount++
        }
      }
      
      // 显示删除结果统计
      if (successCount > 0) {
        ElMessage.success(`成功删除 ${successCount} 个文件`)
      }
      if (failCount > 0) {
        ElMessage.error(`${failCount} 个文件删除失败`)
      }
      
      // 删除操作完成后重新获取当前页数据
      fetchFileList()
    } catch (error) {
      console.error('批量删除文件失败:', error)
      ElMessage.error('批量删除文件失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除操作，不做处理
  })
}

// 分析结论标签类型
const getResultTagType = (result) => {
  if (!result) return 'info';
  
  // 积极类词汇
  const positiveWords = ['积极', '希望', '乐观', '信心', '感恩'];
  
  // 消极类词汇
  const negativeWords = ['消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', 
    '愤怒', '敌视', '报复', '强迫', '疑病', '自暴自弃', '自卑', '偏执']; 
  
  // 中性词汇
  const neutralWords = ['自满', '自负', '孤独', '不合群', '依赖', '从众', '猜忌', 
    '信任', '嫉妒', '敏感', '空虚', '压抑', '逆反', '逆来顺受', '懒散', '怀旧', 
    '挑战', '后悔', '愧疚', '麻木', '逃避'];
  
  // 拆分结果中可能的多个词语
  const words = result.split('、');
  
  // 检查是否包含消极词汇
  if (words.some(word => negativeWords.some(nw => word.includes(nw)))) {
    return 'danger';
  }
  
  // 检查是否包含积极词汇
  if (words.some(word => positiveWords.some(pw => word.includes(pw)))) {
    return 'success';
  }
  
  // 默认为警告级别
  return 'warning';
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page
  fetchFileList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换每页条数时重置为第一页
  fetchFileList()
}

// 解码文件名
const decodeFileName = (filename) => {
  try {
    return decodeURIComponent(filename)
  } catch (e) {
    return filename
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(2)} ${units[index]}`
}

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '未解析': 'info',
    '解析中': 'warning',
    '解析成功': 'success',
    '解析失败': 'danger'
  }
  return statusMap[status] || 'info'
}
</script> 

<style scoped>
/* 批量操作区域样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 8px;
}

.batch-info {
  display: flex;
  align-items: center;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

/* 结论文本样式 */
.conclusion-text {
  font-weight: bold;
  color: #000000;
}

.conclusion-failed {
  color: #606266;
  font-style: italic;
}

.conclusion-processing {
  color: #606266;
}
</style> 