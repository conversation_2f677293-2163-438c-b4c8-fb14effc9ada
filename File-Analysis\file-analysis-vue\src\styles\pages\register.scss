.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-image: url('@/assets/dashboard.jpg');
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(10, 46, 76, 0.3);
    z-index: 1;
  }
  
  &::after {
    content: '';
    position: absolute;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(0, 255, 220, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    top: -400px;
    left: -400px;
    border-radius: 50%;
    z-index: 2;
    animation: pulse 15s infinite alternate;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(0.8);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

.register-card {
  width: 400px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 3;
}

.register-title {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #00ffa3, #00e5ff);
  }
}

.register-button {
  width: 100%;
  background: linear-gradient(90deg, #00ffa3, #00e5ff);
  border: none;
  height: 44px;
  font-weight: 600;
  
  &:hover, &:focus {
    background: linear-gradient(90deg, #00eb96, #00d0e7);
    box-shadow: 0 0 15px rgba(0, 255, 163, 0.5);
  }
}

.login-link {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
  
  a {
    color: #00ffa3;
    text-decoration: none;
    font-weight: 600;
    position: relative;
    
    &:hover {
      color: #00ffa3;
      
      &::after {
        width: 100%;
      }
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 1px;
      background: linear-gradient(90deg, #00ffa3, #00e5ff);
      transition: width 0.3s ease;
    }
  }
}

:deep(.el-form-item__label) {
  color: #ffffff;
  font-weight: 500;
} 