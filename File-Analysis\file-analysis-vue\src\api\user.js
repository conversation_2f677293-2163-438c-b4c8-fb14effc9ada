import request from '@/utils/request'

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {String} data.username - 用户名
 * @param {String} data.password - 密码
 * @param {String} data.email - 邮箱
 * @param {String} [data.phone] - 手机号(可选)
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return request({
    url: '/api/register',
    method: 'post',
    data
  })
}

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {String} data.username - 用户名或邮箱
 * @param {String} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  return request({
    url: '/api/login',
    method: 'post',
    data
  })
}

/**
 * 获取用户信息
 * @param {Number} userId - 用户ID
 * @returns {Promise} - 返回用户信息
 */
export function getUserInfo(userId) {
  return request({
    url: `/api/users/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户个人资料
 * @param {Number} userId - 用户ID
 * @param {Object} data - 更新的资料
 * @param {String} [data.email] - 邮箱
 * @param {String} [data.phone] - 手机号
 * @returns {Promise} - 返回更新结果
 */
export function updateUserProfile(userId, data) {
  return request({
    url: `/api/users/${userId}/profile`,
    method: 'put',
    data
  })
}

/**
 * 修改用户密码
 * @param {Number} userId - 用户ID
 * @param {Object} data - 密码信息
 * @param {String} data.old_password - 旧密码
 * @param {String} data.new_password - 新密码
 * @returns {Promise} - 返回修改结果
 */
export function changePassword(userId, data) {
  return request({
    url: `/api/users/${userId}/password`,
    method: 'put',
    data
  })
}

/**
 * 管理员 - 获取所有用户列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 当前页码
 * @param {Number} params.page_size - 每页大小
 * @returns {Promise} - 返回用户列表
 */
export function getAllUsers(params) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  })
}

/**
 * 管理员 - 创建用户
 * @param {Object} data - 用户信息
 * @param {String} data.username - 用户名
 * @param {String} data.password - 密码
 * @param {String} data.email - 邮箱
 * @param {String} [data.phone] - 手机号(可选)
 * @param {String} [data.role] - 用户角色(user/admin)
 * @param {Number} [data.status] - 账户状态(1启用/0禁用)
 * @returns {Promise} - 返回创建结果
 */
export function createUser(data) {
  return request({
    url: '/api/admin/users',
    method: 'post',
    data
  })
}

/**
 * 管理员 - 更新用户信息
 * @param {Number} userId - 用户ID
 * @param {Object} data - 要更新的用户信息
 * @returns {Promise} - 返回更新结果
 */
export function updateUser(userId, data) {
  return request({
    url: `/api/admin/users/${userId}`,
    method: 'put',
    data
  })
}

/**
 * 管理员 - 删除用户
 * @param {Number} userId - 用户ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteUser(userId) {
  return request({
    url: `/api/admin/users/${userId}`,
    method: 'delete'
  })
} 