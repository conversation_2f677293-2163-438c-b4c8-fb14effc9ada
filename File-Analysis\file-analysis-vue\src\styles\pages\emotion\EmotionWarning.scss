// 情绪预警页面样式

.warning-wrapper {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .title {
        font-size: 22px;
        font-weight: 600;
      }
    }
    
    .right {
      display: flex;
      gap: 10px;
    }
  }
  
  .filter-container {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .filter-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 20px;
    }
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .el-form-item {
        margin-bottom: 0;
        min-width: 250px;
      }
    }
    
    .filter-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .warning-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    
    .warning-card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        .icon {
          font-size: 24px;
          color: #f56c6c;
          margin-right: 10px;
        }
        
        .title {
          font-size: 18px;
          font-weight: 500;
        }
      }
      
      .card-content {
        margin-bottom: 15px;
        
        .info-item {
          display: flex;
          margin-bottom: 10px;
          
          .label {
            width: 80px;
            color: #606266;
          }
          
          .value {
            flex: 1;
            color: #303133;
          }
        }
        
        .progress-item {
          margin-bottom: 10px;
          
          .label {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            
            .name {
              color: #606266;
            }
            
            .value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
      
      .card-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }
  }
  
  .data-container {
    flex-grow: 1;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .tab-container {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      
      .el-tabs__content {
        flex-grow: 1;
        overflow: auto;
        padding: 20px 0;
      }
    }
    
    .chart-container {
      height: 400px;
      margin-bottom: 30px;
    }
    
    .table-container {
      flex-grow: 1;
      
      .pagination-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }
  }
  
  .warning-detail-drawer {
    .detail-header {
      padding-bottom: 20px;
      margin-bottom: 20px;
      border-bottom: 1px solid #ebeef5;
      
      .title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        
        &.high {
          background-color: #f56c6c;
        }
        
        &.medium {
          background-color: #e6a23c;
        }
        
        &.low {
          background-color: #67c23a;
        }
      }
    }
    
    .detail-content {
      .section {
        margin-bottom: 30px;
        
        .section-title {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .info-list {
          .info-item {
            display: flex;
            margin-bottom: 15px;
            
            .label {
              width: 120px;
              color: #606266;
            }
            
            .value {
              flex: 1;
              color: #303133;
            }
          }
        }
      }
      
      .chart-section {
        .chart-container {
          height: 300px;
          margin-bottom: 20px;
        }
      }
    }
    
    .action-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 30px;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
} 