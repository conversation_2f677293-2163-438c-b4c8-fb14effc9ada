.file-list-container {
    padding: 0;
    background-color: #fff;
    
    /* 筛选区域样式 - 开始 */
    .filter-container {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      background-color: #fff;
      border-bottom: 1px solid #e8eaec;
      position: relative;
      
      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        align-items: center;
        justify-content: flex-start;
      }
      
      /* 筛选组样式 - 日期、状态、排序等 */
      .filter-group {
        display: flex;
        align-items: center;
        height: 36px;
        margin-right: 10px;
        
        .filter-label {
          font-size: 13px;
          color: #515a6e;
          font-weight: 500;
          margin-right: 8px;
          white-space: nowrap;
          width: 42px;
          text-align: right;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
        
        /* 确保筛选组内的按钮组统一间距 */
        .el-radio-group {
          display: flex;
          gap: 10px;
        }
        
        /* 调整排序、状态和大小组的间距 */
        &.sort-group, &.status-group, &.size-group {
          .filter-label {
            margin-right: 8px;
          }
        }
        
        &.date-group, &.name-group {
          width: 160px;
          margin-right: 10px;
          
          .date-input, .name-input {
            width: 100%;
            
            :deep(.el-input__wrapper) {
              height: 32px;
              padding: 0 8px;
              box-shadow: 0 0 0 1px #dcdfe6 inset !important;
              
              &:hover {
                box-shadow: 0 0 0 1px #c0c4cc inset !important;
              }
              
              &.is-focus {
                box-shadow: 0 0 0 1px #409eff inset !important;
              }
              
              .el-input__inner {
                height: 32px;
                line-height: 32px;
                font-size: 12px;
              }
            }
            
            :deep(.el-input__prefix), :deep(.el-input__suffix) {
              display: flex;
              align-items: center;
            }
          }
        }
        
        &.status-group {
          width: auto;
          display: flex;
          align-items: center;
          gap: 10px;
          
          .el-button {
            margin-left: 4px;
          }
          
          .el-radio-group {
            display: flex;
            gap: 10px;
          }
        }
        
        &.size-group {
          width: auto;
          display: flex;
          align-items: center;
          gap: 10px;
          
          .el-button {
            margin-left: 4px;
          }
          
          .size-filter-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 40px;
            
            .el-radio-group {
              display: flex;
              gap: 10px;
            }
          }
        }
        
        /* 排序组单选按钮组样式 */
        &.sort-group {
          width: auto;
          
          .sort-selects {
            display: flex;
            align-items: center;
            gap: 40px;
            
            .el-radio-group {
              display: flex;
              gap: 10px;
            }
          }
        }
      }
      
      .filter-actions {
        display: flex;
        gap: 8px;
        
        &.first-row-actions {
          margin-left: auto;
        }
        
        &.second-row-actions {
          margin-left: auto;
        }
        
        .el-button {
          height: 32px;
          padding: 0 12px;
        }
      }
      
      /* 下拉选择器样式 */
      :deep(.el-select) {
        width: 160px;
        height: 32px;
        
        .el-input {
          height: 32px;
        }
        
        .el-select__wrapper {
          font-size: 12px;
          gap: 4px;
          line-height: 20px;
          min-height: 32px;
          padding: 2px 8px;
        }
        
        /* 排序字段选择器 */
        &.sort-field {
          width: 90px;
          
          .el-input {
            .el-input__wrapper {
              border-radius: 4px;
            }
          }
        }
        
        /* 排序方向选择器 */
        &.sort-order {
          width: 90px;
          height: 32px;
          
          .el-input {
            .el-input__wrapper {
              border-radius: 4px;
            }
          }
        }
      }
      
      /* 输入框通用样式 */
      :deep(.el-input__wrapper) {
        height: 32px;
        box-shadow: 0 0 0 1px #dcdfe6 inset !important;
        border-radius: 4px;
        transition: all 0.25s;
        background-color: #fff;
        font-size: 12px;
        gap: 4px;
        line-height: 20px;
        min-height: 32px;
        padding: 2px 8px;
        
        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset !important;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px #409eff inset !important;
        }
      }
      
      :deep(.el-input__inner) {
        font-size: 12px;
        height: 32px;
        line-height: 32px;
        padding: 0;
        color: #303133;
        font-weight: 400;
      }
      
      :deep(.el-select__wrapper) {
        background-color: #fff;
        border-radius: 4px;
        padding: 0 8px;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
      
      :deep(.el-range-editor.el-input__wrapper) {
        padding: 2px 8px;
      }
      
      :deep(.filter-input) {
        .el-input__wrapper {
          border: 1px solid #dcdfe6;
          box-shadow: none !important;
          
          &:hover {
            border-color: #c0c4cc;
          }
          
          &.is-focus {
            border-color: #409eff;
            box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
          }
        }
      }
      
      /* 单选按钮组样式统一 */
      :deep(.el-radio-group) {
        display: flex;
        gap: 10px;
        
        .el-radio {
          margin-right: 0;
          margin-left: 0;
          padding: 0 6px;
          line-height: 32px;
          
          .el-radio__label {
            color: #606266;
            padding-left: 10px;
            font-size: 14px;
            font-weight: 500;
            line-height: 1;
          }
          
          .el-radio__inner {
            width: 14px;
            height: 14px;
          }
        }
      }
      
      /* 日期选择器样式修复 */
      :deep(.el-date-editor) {
        width: 100% !important;
        
        .el-input__wrapper {
          padding: 0 8px !important;
        }
        
        /* 确保日期选择器和其他输入框垂直对齐 */
        &.el-date-editor--date {
          margin-top: 0 !important;
          
          .el-input__inner {
            padding-left: 30px !important;
          }
          
          .el-input__prefix {
            left: 8px !important;
          }
        }
      }
    }
    /* 筛选区域样式 - 结束 */
    
    .filename-cell {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        color: #41b883;
      }
      
      .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }
    }
    
    /* 状态容器样式 */
    .status-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .el-tag {
        margin-right: 0;
        align-self: center;
      }

      .progress-wrapper {
        width: 100%;
        max-width: 150px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
      }
      
      .status-progress {
        flex-grow: 1;
        margin-left: 0;
        height: 6px;

        &.is-failure {
          .el-progress-bar__outer {
            background-color: #fde2e2;
          }
          .el-progress-bar__inner {
            background-color: #f56c6c;
          }
        }
      }

      .progress-percentage {
        font-size: 12px;
        color: #606266;
        min-width: 32px;
        text-align: right;
      }

      .error-warning {
        position: relative;
        width: 24px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: transparent;
        
        .el-icon {
          color: #f56c6c;
          font-size: 24px;
          line-height: 1;
        }
      }
    }
    
    :deep(.el-button) {
      margin-left: 10px;
      margin-right: 10px;
    }
    
    .pagination-container {
      margin: 20px 0;
      display: flex;
      justify-content: center;
      padding: 0 20px;
      
      :deep(.el-pagination) {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        
        .el-pagination__total {
          margin-right: 16px;
        }
        
        .el-pagination__sizes {
          margin-right: 16px;
        }
      }
    }
    
    :deep(.el-card) {
      border-radius: 0;
      margin: 0;
      box-shadow: none;
      border: none;
      
      .el-card__header {
        padding: 12px 16px;
      }
      
      .el-card__body {
        padding: 0;
      }
    }
    
    :deep(.el-table) {
      background-color: #fff;
      
      .el-table__header th {
        background-color: #f5f7fa;
        color: #515a6e;
        font-weight: 500;
      }
      
      .el-button--primary {
        background-color: #41b883;
        border-color: #41b883;
        
        &:hover, &:focus {
          background-color: #5cc296;
          border-color: #5cc296;
        }
      }
      
      .el-button--danger {
        background-color: #f56c6c;
        
        &:hover, &:focus {
          background-color: #f78989;
          border-color: #f78989;
        }
      }
    }
    
    :deep(.el-pagination) {
      background-color: #fff;
      padding: 16px 0;
      border-top: 1px solid #e8eaec;
      
      .btn-prev, .btn-next, .el-pager li {
        background-color: #fff;
        color: #515a6e;
        
        &.is-active {
          color: #fff;
          background-color: #41b883;
        }
        
        &:hover {
          color: #41b883;
        }
      }
      
      .el-pagination__total,
      .el-pagination__sizes {
        color: #515a6e;
      }
    }
    
    :deep(.el-radio-group) {
      display: flex;
      gap: 10px;
      
      .el-radio {
        margin-right: 0;
        margin-left: 0;
        padding: 0 5px;
        
        .el-radio__label {
          color: #515a6e;
          padding-left: 10px;
          font-size: 12px;
        }
        
        .el-radio__inner {
          width: 12px;
          height: 12px;
        }
        
        &.is-checked {
          .el-radio__label {
            color: #41b883;
          }
          
          .el-radio__inner {
            border-color: #41b883;
            background: #fff;
            
            &::after {
              background-color: #41b883;
              width: 6px;
              height: 6px;
            }
          }
        }
      }
    }
    
    .button-group {
      display: flex;
      justify-content: center;
      gap: 10px;
      
      .el-button {
        margin: 0;
        padding: 6px 10px;
        font-size: 12px;
      }
    }
  } 