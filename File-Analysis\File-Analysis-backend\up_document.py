import os
import datetime
from werkzeug.utils import secure_filename
from file_db import save_file_record
from config import UPLOAD_CONFIG
import urllib.parse
import shutil
import traceback

class UploadDocument:
    def __init__(self):
        self.upload_folder = UPLOAD_CONFIG['UPLOAD_FOLDER']
        self.allowed_extensions = UPLOAD_CONFIG['ALLOWED_EXTENSIONS']
        self.max_content_length = UPLOAD_CONFIG['MAX_CONTENT_LENGTH']

        # 确保上传目录存在
        if not os.path.exists(self.upload_folder):
            print(f"创建上传目录: {self.upload_folder}")
            os.makedirs(self.upload_folder)
        else:
            print(f"上传目录已存在: {self.upload_folder}")

    def allowed_file(self, filename):
        """检查文件扩展名是否允许"""
        if '.' not in filename:
            print(f"文件名 {filename} 没有扩展名")
            return False
        
        ext = filename.rsplit('.', 1)[1].lower()
        is_allowed = ext in self.allowed_extensions
        print(f"文件扩展名: {ext}, 是否允许: {is_allowed}")
        return is_allowed

    def validate_file(self, file):
        """验证文件
        Args:
            file: FileStorage对象
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not file:
            print("文件对象为空")
            return False, "没有文件"

        if file.filename == '':
            print("文件名为空")
            return False, "没有选择文件"

        if not self.allowed_file(file.filename):
            print(f"不支持的文件类型: {file.filename}")
            return False, "不支持的文件类型"

        return True, None
    
    def convert_wav_file(self, original_path, target_path):
        """转换WAV文件为符合语音识别要求的格式
        Args:
            original_path: 原始文件路径
            target_path: 目标文件路径
        Returns:
            bool: 是否成功
        """
        try:
            from pydub import AudioSegment
            
            # 指定 ffmpeg 可执行文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            ffmpeg_path = os.path.join(current_dir, "model", "ffmpeg-7.1.1-essentials_build", "bin", "ffmpeg.exe")
            
            # 检查ffmpeg是否存在
            if not os.path.exists(ffmpeg_path):
                print(f"错误: ffmpeg可执行文件不存在于 {ffmpeg_path}")
                return False
                
            print(f"使用ffmpeg: {ffmpeg_path}")
            AudioSegment.converter = ffmpeg_path

            # 加载音频文件
            print("加载WAV文件...")
            audio = AudioSegment.from_wav(original_path)
            print(f"WAV文件加载成功: 通道数={audio.channels}, 采样率={audio.frame_rate}, 样本宽度={audio.sample_width}")

            # 转换为单声道
            print("转换为单声道...")
            audio = audio.set_channels(1)

            # 转换采样率为 16kHz
            print("转换采样率为16kHz...")
            audio = audio.set_frame_rate(16000)

            # 转换为 16 位深度
            print("转换为16位样本宽度...")
            audio = audio.set_sample_width(2)

            # 保存转换后的音频文件
            print(f"导出转换后的WAV文件: {target_path}")
            audio.export(target_path, format="wav")
            print("导出成功")
            
            return True
        except Exception as e:
            print(f"转换WAV文件失败: {str(e)}")
            traceback.print_exc()
            return False

    def save_file(self, file):
        """保存文件并记录到数据库
        Args:
            file: FileStorage对象
        Returns:
            tuple: (是否成功, 结果数据或错误信息)
        """
        # 验证文件
        is_valid, error_message = self.validate_file(file)
        if not is_valid:
            return False, error_message

        try:
            # 获取原始文件名并进行URL编码（保留中文显示）
            original_filename = file.filename
            print(f"原始文件名: {original_filename}")
            
            # 安全地处理文件名
            safe_filename = secure_filename(original_filename)
            print(f"安全文件名: {safe_filename}")
            
            # 检查是否为WAV文件
            is_wav_file = False
            if '.' in safe_filename and safe_filename.rsplit('.', 1)[1].lower() == 'wav':
                is_wav_file = True
                print("检测到WAV文件")
                
            # 如果不是WAV文件，使用原有的保存方式
            if not is_wav_file:
                # 生成唯一文件名（使用时间戳）
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                unique_filename = timestamp + "_" + safe_filename
                print(f"非WAV文件，唯一文件名: {unique_filename}")
                
                # 保存文件
                file_path = os.path.join(self.upload_folder, unique_filename)
                try:
                    file.save(file_path)
                    print(f"文件已保存到: {file_path}")
                except Exception as e:
                    print(f"保存文件时出错: {str(e)}")
                    return False, f"保存文件时出错: {str(e)}"
                
                # 获取文件大小
                try:
                    file_size = os.path.getsize(file_path)
                    print(f"文件大小: {file_size} 字节")
                except Exception as e:
                    print(f"获取文件大小时出错: {str(e)}")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    return False, f"获取文件大小时出错: {str(e)}"
            else:
                # 处理WAV文件
                # 1. 先保存临时文件
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                temp_filename = f"temp_{timestamp}.wav"
                temp_file_path = os.path.join(self.upload_folder, temp_filename)
                
                try:
                    file.save(temp_file_path)
                    print(f"WAV文件已临时保存到: {temp_file_path}")
                except Exception as e:
                    print(f"保存临时WAV文件时出错: {str(e)}")
                    return False, f"保存临时WAV文件时出错: {str(e)}"
                
                # 2. 设置最终文件名和路径
                final_filename = f"{timestamp}_wav"
                final_file_path = os.path.join(self.upload_folder, final_filename)
                
                # 3. 转换WAV文件
                conversion_success = self.convert_wav_file(temp_file_path, final_file_path)
                
                # 4. 删除临时文件
                if os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                        print(f"临时文件已删除: {temp_file_path}")
                    except Exception as e:
                        print(f"删除临时文件失败: {str(e)}")
                
                # 如果转换失败，返回错误
                if not conversion_success:
                    if os.path.exists(final_file_path):
                        os.remove(final_file_path)
                    return False, "WAV文件转换失败"
                
                # 5. 获取文件大小
                try:
                    file_size = os.path.getsize(final_file_path)
                    print(f"转换后的WAV文件大小: {file_size} 字节")
                except Exception as e:
                    print(f"获取转换后的WAV文件大小时出错: {str(e)}")
                    if os.path.exists(final_file_path):
                        os.remove(final_file_path)
                    return False, f"获取转换后的WAV文件大小时出错: {str(e)}"
                
                # 6. 设置要保存到数据库的文件名
                unique_filename = final_filename
                print(f"WAV文件已转换，最终文件名: {unique_filename}")
                
            # 保存到数据库
            print("开始保存文件记录到数据库...")
            success, result = save_file_record(original_filename, file_size, unique_filename)
            
            if not success:
                print(f"数据库操作失败: {result}")
                # 如果数据库操作失败，删除已上传的文件
                file_path_to_delete = os.path.join(self.upload_folder, unique_filename)
                if os.path.exists(file_path_to_delete):
                    os.remove(file_path_to_delete)
                return False, result  # 返回错误信息
            
            # 成功保存到数据库
            file_id = result
            print(f"文件记录已保存，ID: {file_id}")
            
            return True, {
                "message": "文件上传成功",
                "file_id": file_id,
                "filename": urllib.parse.quote(original_filename),
                "filesize": file_size,
                "unique_filename": unique_filename
            }
                
        except Exception as e:
            print(f"文件上传过程中发生异常: {str(e)}")
            traceback.print_exc()
            return False, f"文件上传失败: {str(e)}"
