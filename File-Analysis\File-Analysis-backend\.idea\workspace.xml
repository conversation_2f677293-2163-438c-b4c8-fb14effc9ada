<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cf685ace-3d28-4db3-b4ad-a9b2f7b456fa" name="Changes" comment="第二次修改">
      <change beforePath="$PROJECT_DIR$/../../File Analysis/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/131034.wav" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/package-lock.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/public/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/public/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/public/vite.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/App.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/api/file.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/api/user.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/assets/avatar.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/assets/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/assets/vue.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/assets/welcome.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/components/FileUploadDialog.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/layouts/MainLayout.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/main.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/Dashboard.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/Login.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/Register.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/UserProfile.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/file/FileList.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/system/FileManage.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/pages/system/UserManage.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/router/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/store/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/store/user.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/style.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/app.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/components/fileUploadDialog.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/index.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/layouts/mainLayout.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/dashboard.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/fileList.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/fileManage.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/fileUpload.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/login.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/pages/register.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/styles/variables.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/src/utils/request.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../File Analysis/file-analysis-vue/vite.config.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Flask Main" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2xRFyAM7dXUkVlrHWjajM4ywbgv" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Flask server.Flask (app.py) (1).executor": "Run",
    "Flask server.Flask (app.py).executor": "Run",
    "Python.speech_to_text (1).executor": "Run",
    "Python.sql_connect.executor": "Run",
    "Python.szr_speech_to_text (1).executor": "Run",
    "Python.szr_speech_to_text.executor": "Run",
    "Python.totest (1).executor": "Run",
    "Python.vosk.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "deletionFromPopupRequiresConfirmation": "false",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/2025bysj/000_rgzn_zjt/File-Analysis/File-Analysis-backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\szr\util" />
    </key>
  </component>
  <component name="RunManager" selected="Flask server.Flask (app.py)">
    <configuration name="db_connect" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="File Analysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="E:\szr\db_connect.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="speech_to_text" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="File Analysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="E:\szr\speech_to_text.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Flask (app.py)" type="Python.FlaskServer" nameIsGenerated="true">
      <option name="flaskDebug" value="true" />
      <module name="File Analysis" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="szr" type="Python.FlaskServer">
      <module name="File Analysis" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Flask server.Flask (app.py)" />
      <item itemvalue="Flask server.szr" />
      <item itemvalue="Python.db_connect" />
      <item itemvalue="Python.speech_to_text" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.db_connect" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cf685ace-3d28-4db3-b4ad-a9b2f7b456fa" name="Changes" comment="" />
      <created>1747893231308</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747893231308</updated>
      <workItem from="1747893234430" duration="3599000" />
      <workItem from="1747897187807" duration="2563000" />
      <workItem from="1747899992069" duration="874000" />
      <workItem from="1747900901148" duration="2461000" />
      <workItem from="1747912330422" duration="2748000" />
      <workItem from="1747919294450" duration="703000" />
      <workItem from="1747964274734" duration="5514000" />
      <workItem from="1747971119461" duration="1600000" />
      <workItem from="1747980650639" duration="3979000" />
      <workItem from="1747985816258" duration="2191000" />
      <workItem from="1748189272693" duration="2439000" />
      <workItem from="1748223479871" duration="764000" />
      <workItem from="1748225538496" duration="11905000" />
      <workItem from="1748259848410" duration="9423000" />
      <workItem from="1748315355276" duration="11962000" />
      <workItem from="1748363420134" duration="2795000" />
      <workItem from="1748395185414" duration="14491000" />
      <workItem from="1748486203766" duration="8115000" />
      <workItem from="1748569915608" duration="8289000" />
      <workItem from="1748659880045" duration="7418000" />
      <workItem from="1748930045825" duration="7158000" />
      <workItem from="1748957699386" duration="55000" />
      <workItem from="1749006274737" duration="1315000" />
      <workItem from="1749021309781" duration="3236000" />
      <workItem from="1749108051330" duration="2789000" />
      <workItem from="1749173215355" duration="4479000" />
      <workItem from="1749184216116" duration="3231000" />
      <workItem from="1749719631536" duration="1778000" />
      <workItem from="1749781615162" duration="10682000" />
      <workItem from="1749806383378" duration="1816000" />
      <workItem from="1749814597428" duration="217000" />
      <workItem from="1749970292155" duration="85000" />
      <workItem from="1750036241952" duration="15718000" />
      <workItem from="1750070440140" duration="777000" />
      <workItem from="1750137378578" duration="5485000" />
      <workItem from="1750195244622" duration="3100000" />
      <workItem from="1750209100231" duration="4689000" />
      <workItem from="1750320836946" duration="1550000" />
      <workItem from="1750381706620" duration="2145000" />
      <workItem from="1750399759260" duration="4332000" />
      <workItem from="1750407474010" duration="1887000" />
      <workItem from="1750728914940" duration="9392000" />
      <workItem from="1750813901764" duration="98000" />
      <workItem from="1750814210071" duration="7276000" />
    </task>
    <task id="LOCAL-00001" summary="第二次修改">
      <option name="closed" value="true" />
      <created>1750409113222</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750409113222</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第二次修改" />
    <option name="LAST_COMMIT_MESSAGE" value="第二次修改" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/szr$totest__1_.coverage" NAME="totest (1) Coverage Results" MODIFIED="1747894126949" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$Flask__app_py___1_.coverage" NAME="Flask (app.py) (1) Coverage Results" MODIFIED="1747969980188" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/szr$speech_to_text__1_.coverage" NAME="speech_to_text (1) Coverage Results" MODIFIED="1747984577486" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$sql_connect.coverage" NAME="sql_connect Coverage Results" MODIFIED="1747969374712" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$speech_to_text.coverage" NAME="speech_to_text Coverage Results" MODIFIED="1747984531080" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$szr_speech_to_text.coverage" NAME="szr_speech_to_text Coverage Results" MODIFIED="1747965237376" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$vosk.coverage" NAME="vosk Coverage Results" MODIFIED="1747893991175" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$Flask__app_py_.coverage" NAME="Flask (app.py) Coverage Results" MODIFIED="1747986396332" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/szr$szr_speech_to_text__1_.coverage" NAME="szr_speech_to_text (1) Coverage Results" MODIFIED="1747964930284" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/File_Analysis$Flask__app_py_.coverage" NAME="Flask (app.py) Coverage Results" MODIFIED="1750830725470" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>