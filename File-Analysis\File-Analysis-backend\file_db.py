import datetime
import urllib.parse
import os
from db_utils import execute_query, execute_insert, execute_update, format_datetime, get_paginated_data
from config import REPORTS_CONFIG

def save_file_record(filename, filesize, unique_filename):
    """
    保存文件记录到数据库
    :param filename: 文件名
    :param filesize: 文件大小
    :param unique_filename: 唯一文件名
    :return: (是否成功, 文件ID或错误信息)
    """
    sql = """
    INSERT INTO uploaded_files 
    (filename, filesize, upload_time, status, analysis_result, unique_filename)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    encoded_filename = urllib.parse.quote(filename)
    params = (encoded_filename, filesize, datetime.datetime.now(), '未解析', None, unique_filename)
    
    return execute_insert(sql, params)

def get_files():
    """
    获取所有文件列表（已废弃，使用get_files_paginated代替）
    :return: 文件列表
    """
    sql = "SELECT * FROM uploaded_files ORDER BY id DESC"
    result = execute_query(sql)
    
    # 处理datetime对象
    if result:
        for row in result:
            format_datetime(row)
    
    return result

def get_files_paginated(page=1, page_size=10, sort_field='id', sort_order='desc', status_filter=None, size_operator=None, size_value=None, name_filter=None, date_range=None):
    """
    获取分页文件列表
    :param page: 页码
    :param page_size: 每页条数
    :param sort_field: 排序字段
    :param sort_order: 排序方向
    :param status_filter: 状态筛选
    :param size_operator: 大小操作符
    :param size_value: 大小值
    :param name_filter: 名称筛选
    :param date_range: 日期范围
    :return: 文件列表和总数
    """
    # 构建查询条件
    conditions = []
    params = []
    
    if status_filter and status_filter != '全部':
        conditions.append("status = %s")
        params.append(status_filter)
    
    if size_operator and size_operator != '全部' and size_value and size_value != 'other':
        size_in_bytes = int(size_value) * 1024 * 1024  # 转换为字节
        if size_operator == '>=':
            conditions.append("filesize >= %s")
            params.append(size_in_bytes)
        elif size_operator == '<=':
            conditions.append("filesize <= %s")
            params.append(size_in_bytes)
    
    if name_filter:
        conditions.append("filename LIKE %s")
        params.append(f"%{name_filter}%")
    
    if date_range and len(date_range) == 2:
        conditions.append("DATE(upload_time) BETWEEN %s AND %s")
        params.append(date_range[0])
        params.append(date_range[1])
    
    # 构建WHERE子句
    where_clause = " AND ".join(conditions) if conditions else ""
    
    # 验证排序字段
    valid_sort_fields = ['id', 'filename', 'filesize', 'upload_time']
    if sort_field not in valid_sort_fields:
        sort_field = 'id'
    
    # 验证排序方向
    sort_order = sort_order.upper()
    if sort_order not in ['ASC', 'DESC']:
        sort_order = 'DESC'
    
    # 使用通用分页查询
    order_by = f"{sort_field} {sort_order}"
    return get_paginated_data('uploaded_files', page, page_size, where_clause, params, order_by)

def get_file_by_id(file_id):
    """
    根据ID获取文件详情
    :param file_id: 文件ID
    :return: 文件详情
    """
    sql = "SELECT * FROM uploaded_files WHERE id = %s"
    result = execute_query(sql, (file_id,))
    
    if result and len(result) > 0:
        # 将datetime对象转换为字符串
        file_data = result[0]
        format_datetime(file_data)
        return file_data
    return None

def update_analysis_result_file(file_id, analysis_content, file_format='txt'):
    """
    将分析结果保存为外部文件，并更新数据库中的analysis_result字段为文件名
    
    :param file_id: 文件ID
    :param analysis_content: 分析结果内容或已保存的文件名(docx文件)
    :param file_format: 文件格式，默认为txt，支持txt、pdf、docx
    :return: (是否成功, 消息)
    """
    # 获取文件信息
    file_data = get_file_by_id(file_id)
    if not file_data:
        return False, "文件记录不存在"
    
    # 使用配置中的reports文件夹路径
    reports_dir = REPORTS_CONFIG['REPORTS_FOLDER']
    if not os.path.exists(reports_dir):
        os.makedirs(reports_dir)
    
    # 打印路径用于调试
    print(f"报告保存路径: {reports_dir}")
    
    # 处理已生成的文件名情况(当docx文件已经生成时)
    if file_format == 'docx' and os.path.exists(os.path.join(reports_dir, analysis_content)):
        result_filename = analysis_content
    else:
        # 生成分析结果文件名
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        result_filename = f"analysis_{file_id}_{timestamp}.{file_format}"
        result_file_path = os.path.join(reports_dir, result_filename)
        
        try:
            # 根据文件类型选择写入模式
            if file_format in ['docx', 'xlsx', 'pdf']:
                # 二进制文件不应通过这个函数写入，应该已在其他地方处理
                if isinstance(analysis_content, str) and os.path.exists(analysis_content):
                    # 如果这是一个已存在的文件路径，我们只需要更新数据库
                    result_filename = os.path.basename(analysis_content)
                else:
                    return False, f"不支持通过此函数直接写入{file_format}文件"
            else:
                # 文本文件直接写入
                with open(result_file_path, 'w', encoding='utf-8') as f:
                    f.write(analysis_content)
        except Exception as e:
            # 如果发生错误，更新状态为解析失败
            error_message = str(e)
            error_sql = """
            UPDATE uploaded_files 
            SET status = '解析失败', analysis_result = %s
            WHERE id = %s
            """
            execute_update(error_sql, (f"保存分析结果文件失败: {error_message}", file_id))
            return False, f"保存分析结果文件失败: {error_message}"
    
    # 更新数据库中的analysis_result字段为文件名
    sql = """
    UPDATE uploaded_files 
    SET analysis_result = %s, status = '解析成功'
    WHERE id = %s
    """
    success, _ = execute_update(sql, (result_filename, file_id))
    
    if success:
        return True, f"分析结果已保存到文件: {result_filename}"
    else:
        return False, "更新数据库记录失败"

def get_dangerous_emotion_files(page=1, page_size=10, sort_field='id', sort_order='desc'):
    """
    获取分析结果中包含危险情感的文件列表
    :param page: 页码
    :param page_size: 每页条数
    :param sort_field: 排序字段
    :param sort_order: 排序方向
    :return: 包含危险情感的文件列表和总数
    """
    # 从lj.txt中提取的危险情感关键词
    dangerous_emotions = [
        '消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', '愤怒', '敌视',
        '报复心理', '强迫观念', '疑病', '自暴自弃', '自满', '自负', '自卑', '偏执',
        '孤独', '不合群', '依赖', '从众心理', '猜忌', '嫉妒', '敏感', '空虚',
        '压抑', '逆反', '逆来顺受', '懒散', '后悔', '愧疚', '麻木', '逃避现实',
        '报复', '羞耻', '执念', '易怒', '冷漠', '贪婪', '虚荣', '成瘾性依赖',
        '精神内耗'
    ]
    
    # 构建查询条件：分析成功且分析结果包含危险情感关键词
    conditions = ["status = '解析成功'"]
    
    # 使用OR连接所有情感关键词的条件
    emotion_conditions = []
    params = []
    
    for emotion in dangerous_emotions:
        # 查询analysis_result文件内容包含情感关键词的记录
        # 注意：这里假设我们能访问到分析结果文件的内容
        # 实际实现可能需要调整，如果analysis_result只存储了文件路径而非内容
        emotion_conditions.append("analysis_result LIKE %s")
        params.append(f"%{emotion}%")
    
    # 将所有情感条件用OR连接
    if emotion_conditions:
        conditions.append(f"({' OR '.join(emotion_conditions)})")
    
    # 构建WHERE子句
    where_clause = " AND ".join(conditions)
    
    # 验证排序字段
    valid_sort_fields = ['id', 'filename', 'filesize', 'upload_time']
    if sort_field not in valid_sort_fields:
        sort_field = 'id'
    
    # 验证排序方向
    sort_order = sort_order.upper()
    if sort_order not in ['ASC', 'DESC']:
        sort_order = 'DESC'
    
    # 使用通用分页查询
    order_by = f"{sort_field} {sort_order}"
    return get_paginated_data('uploaded_files', page, page_size, where_clause, params, order_by)

def get_emotion_statistics(limit=100):
    """
    统计最近分析的文件中出现的情感及其次数
    :param limit: 最大记录数，默认100条
    :return: 情感统计数据
    """
    # 获取最近的解析成功的文件记录
    sql = """
    SELECT id, analysis_result 
    FROM uploaded_files 
    WHERE status = '解析成功' AND analysis_result IS NOT NULL 
    ORDER BY id DESC LIMIT %s
    """
    records = execute_query(sql, (limit,))
    
    if not records:
        return {"safe_emotions": [], "dangerous_emotions": []}
    
    # 定义安全和危险情感列表（从lj.txt提取）
    safe_emotions = [
        '积极', '希望', '乐观', '信心', '感恩', '信任', '挑战', '怀旧',
        '平静', '喜悦', '共情', '宽恕', '敬畏', '满足', '勇气', '好奇',
        '奉献'
    ]
    
    dangerous_emotions = [
        '消极', '悲观', '绝望', '抑郁', '焦虑', '恐惧', '恐慌', '愤怒', '敌视',
        '报复心理', '强迫观念', '疑病', '自暴自弃', '自满', '自负', '自卑', '偏执',
        '孤独', '不合群', '依赖', '从众心理', '猜忌', '嫉妒', '敏感', '空虚',
        '压抑', '逆反', '逆来顺受', '懒散', '后悔', '愧疚', '麻木', '逃避现实',
        '报复', '羞耻', '执念', '易怒', '冷漠', '贪婪', '虚荣', '成瘾性依赖',
        '精神内耗'
    ]
    
    # 统计结果
    safe_emotion_counts = {}
    dangerous_emotion_counts = {}
    
    import re
    
    # 遍历记录
    for record in records:
        analysis_result = record['analysis_result'] or ""
        
        # 提取情感词汇列表
        emotions = []
        # 匹配"经分析，当前主体情感具有（xxx）的心理特征"模式中的情感词汇
        pattern = r"经分析，当前主体情感具有[（(]([^）)]+)[）)]"
        match = re.search(pattern, analysis_result)
        
        if match:
            # 提取括号中的情感词汇，分成列表
            emotions_str = match.group(1)
            emotions = [e.strip() for e in re.split(r'[,，、]', emotions_str) if e.strip()]
        
        # 如果没有通过正则表达式匹配到，则直接搜索文本中的情感关键词
        if not emotions:
            # 检查文本中是否出现各种情感关键词
            for emotion in safe_emotions:
                if emotion in analysis_result:
                    emotions.append(emotion)
            
            for emotion in dangerous_emotions:
                if emotion in analysis_result:
                    emotions.append(emotion)
        
        # 统计各情感出现次数
        for emotion in emotions:
            if emotion in safe_emotions:
                if emotion in safe_emotion_counts:
                    safe_emotion_counts[emotion] += 1
                else:
                    safe_emotion_counts[emotion] = 1
            elif emotion in dangerous_emotions:
                if emotion in dangerous_emotion_counts:
                    dangerous_emotion_counts[emotion] += 1
                else:
                    dangerous_emotion_counts[emotion] = 1
    
    # 转换为列表格式，便于前端处理
    safe_emotions_list = [{"name": k, "value": v} for k, v in safe_emotion_counts.items()]
    dangerous_emotions_list = [{"name": k, "value": v} for k, v in dangerous_emotion_counts.items()]
    
    # 按照出现次数排序
    safe_emotions_list.sort(key=lambda x: x["value"], reverse=True)
    dangerous_emotions_list.sort(key=lambda x: x["value"], reverse=True)
    
    return {
        "safe_emotions": safe_emotions_list,
        "dangerous_emotions": dangerous_emotions_list
    }