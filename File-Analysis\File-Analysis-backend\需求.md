需求：

根据音频文件（wav）生成文本，利用大模型通过分析文本来总结情感，再利用大模型对情感进行二次总结精简，驱动数字人来读出二次精简总结

前端使用



后端：

数据库szr：建表sql语句如下：

CREATE TABLE uploaded_files (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '序号',
    filename VARCHAR(255) NOT NULL COMMENT '文件名称',
    filesize BIGINT UNSIGNED COMMENT '文件大小(字节)',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status ENUM('未解析', '解析中', '解析成功', '解析失败') DEFAULT '未解析' COMMENT '状态',
    analysis_result TEXT COMMENT '分析结果'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

数据库账号root 密码123456

连接数据库

