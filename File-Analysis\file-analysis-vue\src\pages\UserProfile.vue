<template>
  <div class="user-profile-container">
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">个人资料</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <!-- 基本资料 -->
        <el-tab-pane label="基本资料" name="profile">
          <el-form 
            ref="profileFormRef" 
            :model="profileForm" 
            label-width="100px"
          >
            <el-form-item label="用户名">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
            </el-form-item>
            
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号(选填)" />
            </el-form-item>
            
            <el-form-item label="角色">
              <el-tag :type="userStore.role === 0 ? 'danger' : 'info'">
                {{ userStore.role === 0 ? '管理员' : '普通用户' }}
              </el-tag>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="updateProfile" 
                :loading="profileLoading"
              >
                保存修改
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 修改密码 -->
        <el-tab-pane label="修改密码" name="password">
          <el-form 
            ref="passwordFormRef" 
            :model="passwordForm" 
            label-width="100px"
          >
            <el-form-item label="原密码" prop="oldPassword">
              <el-input 
                v-model="passwordForm.oldPassword" 
                type="password" 
                placeholder="请输入原密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input 
                v-model="passwordForm.newPassword" 
                type="password" 
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input 
                v-model="passwordForm.confirmPassword" 
                type="password" 
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="changePassword" 
                :loading="passwordLoading"
              >
                确认修改
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const activeTab = ref('profile')

// 基本资料
const profileFormRef = ref(null)
const profileForm = reactive({
  username: userStore.username,
  email: userStore.email,
  phone: userStore.phone
})
const profileLoading = ref(false)

// 密码修改
const passwordFormRef = ref(null)
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const passwordLoading = ref(false)

// 更新个人资料 - 移除前端验证，交由后端验证
const updateProfile = async () => {
  profileLoading.value = true
  try {
    // 直接提交数据至后端，由后端进行数据验证
    await userStore.updateProfile({
      email: profileForm.email,
      phone: profileForm.phone
    })
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    console.error('更新个人资料失败:', error)
    // 显示后端返回的错误信息
    ElMessage.error(error.response?.data?.message || '更新个人资料失败')
  } finally {
    profileLoading.value = false
  }
}

// 修改密码 - 移除前端验证，交由后端验证
const changePassword = async () => {
  passwordLoading.value = true
  try {
    // 直接提交数据至后端，由后端进行数据验证
    await userStore.changePassword({
      old_password: passwordForm.oldPassword,
      new_password: passwordForm.newPassword,
      confirm_password: passwordForm.confirmPassword // 传递确认密码供后端验证
    })
    ElMessage.success('密码修改成功')
    
    // 重置表单
    passwordForm.oldPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
    passwordFormRef.value.resetFields()
  } catch (error) {
    console.error('修改密码失败:', error)
    // 显示后端返回的错误信息
    ElMessage.error(error.response?.data?.message || '修改密码失败')
  } finally {
    passwordLoading.value = false
  }
}

// 初始化
onMounted(async () => {
  try {
    // 获取最新的用户信息
    await userStore.getUserInfo()
    
    // 更新表单
    profileForm.username = userStore.username
    profileForm.email = userStore.email
    profileForm.phone = userStore.phone
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<style scoped>
.user-profile-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}
</style> 