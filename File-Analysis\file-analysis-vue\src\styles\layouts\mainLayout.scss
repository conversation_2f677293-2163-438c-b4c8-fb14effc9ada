.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: var(--sidebar-width);
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  overflow: hidden;
  border-right: none;
  
  &.is-collapsed {
    width: 64px;
  }
  
  .logo-container {
    height: 56px;
    line-height: 56px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    background-color: #263445;
    text-align: center;
    
    .logo {
      font-size: 24px;
      color: #fff;
    }
    
    .title {
      color: #fff;
      margin-left: 12px;
      margin-top: 0;
      margin-bottom: 0;
      font-size: 18px;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .header {
    height: 50px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: relative;
    
    .left {
      display: flex;
      align-items: center;
      
      .toggle-button, .refresh-button {
        font-size: 20px;
        cursor: pointer;
        color: #606266;
        margin-right: 16px;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
    
    .system-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .right {
      display: flex;
      align-items: center;
      
      .avatar-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .avatar {
          font-size: 20px;
          margin-right: 8px;
          color: #606266;
        }
        
        .username {
          font-size: 14px;
          color: #606266;
          margin-right: 8px;
        }
        
        .role-tag {
          background-color: #f56c6c;
          color: #fff;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
          display: inline-block;
        }
      }
    }
  }
  
  .app-main {
    flex: 1;
    overflow: auto;
    background-color: #f0f2f5;
  }
}

// 过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

:deep(.el-menu) {
  background-color: #2d3a4b !important;
  border-right: none;
  
  .el-menu-item, .el-sub-menu__title {
    color: #bfcbd9 !important;
    height: 50px;
    line-height: 50px;
    
    &:hover {
      background-color: #263445 !important;
      color: #fff !important;
    }
    
    &.is-active {
      color: #bfcbd9 !important;
      background-color: #001528 !important;
      
      &:after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: #bfcbd9;
      }
    }
  }
  
  .el-sub-menu {
    .el-menu {
      background-color: #1f2d3d !important;
      
      .el-menu-item {
        background-color: #1f2d3d !important;
        
        &:hover {
          background-color: #001528 !important;
        }
        
        &.is-active {
          background-color: #001528 !important;
        }
      }
    }
  }
  
  .el-icon {
    color: inherit;
  }
} 