<template>
  <el-dialog
    v-model="dialogVisible"
    title="文件上传"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="!uploading"
    :show-close="!uploading"
    @closed="resetUpload"
  >
    <div class="upload-container">
      <!-- 拖拽上传区域 -->
      <div 
        class="drop-area" 
        @dragover.prevent 
        @drop.prevent="handleFileDrop"
        :class="{ 'drag-over': isDragging, 'disabled': uploading }"
        @dragenter.prevent="isDragging = true"
        @dragleave.prevent="isDragging = false"
      >
        <el-icon class="upload-icon"><upload-filled /></el-icon>
        <div class="upload-text">
          拖拽文件到此处或 
          <label for="file-input" class="file-label" :class="{ 'disabled': uploading }">点击上传</label>
          <input 
            type="file" 
            id="file-input" 
            ref="fileInput"
            accept=".wav" 
            @change="handleFileSelect" 
            style="display: none;"
            :disabled="uploading"
          />
        </div>
        <div class="upload-tip">
          仅支持 .wav 格式音频文件，文件大小不超过 16GB
        </div>
      </div>
      
      <!-- 已选择文件显示区域 -->
      <div v-if="selectedFile" class="selected-file">
        <div class="file-info">
          <el-icon><document /></el-icon>
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
          <el-button v-if="!uploading" type="text" @click="removeFile" class="remove-btn">
            <el-icon><delete /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 上传进度 -->
      <div v-if="uploadProgress > 0" class="upload-progress">
        <el-progress :percentage="uploadProgress" />
        <div class="progress-text">{{ uploadStatus }}</div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog" :disabled="uploading">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitUpload" 
          :loading="uploading"
          :disabled="!selectedFile || uploading"
        >
          开始上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Document, Delete } from '@element-plus/icons-vue'
import { request } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success'])

// 对话框可见性
const dialogVisible = ref(props.modelValue)

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

const fileInput = ref(null)
const selectedFile = ref(null)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const isDragging = ref(false)

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    if (validateFile(file)) {
      selectedFile.value = file
      console.log('已选择文件:', file)
    } else {
      // 验证失败时清空文件输入
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  }
}

// 处理拖放文件
const handleFileDrop = (event) => {
  if (uploading.value) return
  
  isDragging.value = false
  const file = event.dataTransfer.files[0]
  if (file) {
    if (validateFile(file)) {
      selectedFile.value = file
      console.log('已拖放文件:', file)
    }
  }
}

// 验证文件
const validateFile = (file) => {
  // 检查文件扩展名
  if (!file.name.toLowerCase().endsWith('.wav')) {
    ElMessage.error('仅支持 .wav 格式音频文件')
    return false
  }
  
  // 检查文件大小 (16GB)
  const maxSize = 16 * 1024 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过 16GB')
    return false
  }
  
  return true
}

// 移除已选择的文件
const removeFile = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 提交上传
const submitUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  try {
    uploading.value = true
    uploadStatus.value = '准备上传...'
    uploadProgress.value = 1
    
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    
    // 使用request工具发送请求，它会自动添加用户ID到请求头
    const response = await request({
      url: '/api/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        uploadProgress.value = percentCompleted
        uploadStatus.value = `上传中 ${percentCompleted}%`
      }
    })
    
    console.log('上传响应:', response)
    
    // 记录唯一文件名
    console.log('文件唯一标识:', response.unique_filename)
    
    // 上传成功
    uploadProgress.value = 100
    uploadStatus.value = '上传成功'
    ElMessage.success('文件上传成功')
    
    // 通知父组件上传成功
    emit('upload-success', response)
    
    // 延迟关闭对话框
    setTimeout(() => {
      dialogVisible.value = false
    }, 1000)
  } catch (error) {
    console.error('上传失败:', error)
    uploadStatus.value = '上传失败'
    
    // 显示详细错误信息
    if (error.response) {
      console.error('错误响应:', error.response)
      ElMessage.error(`上传失败: ${error.response.data?.error || error.response.statusText}`)
    } else if (error.request) {
      console.error('请求错误:', error.request)
      ElMessage.error('网络错误，无法连接到服务器')
    } else {
      ElMessage.error(`上传错误: ${error.message}`)
    }
    
    uploading.value = false
  }
}

// 重置上传
const resetUpload = () => {
  selectedFile.value = null
  uploadProgress.value = 0
  uploadStatus.value = ''
  uploading.value = false
  
  // 清空文件输入
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 关闭对话框
const closeDialog = () => {
  if (uploading.value) return
  dialogVisible.value = false
}
</script> 