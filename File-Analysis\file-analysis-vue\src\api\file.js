import request from '@/utils/request'
import { apiBaseUrl } from '@/config'

/**
 * 上传文件
 * @param {File} file - 文件对象
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} - 返回上传结果
 */
export function uploadFile(file, onProgress) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: onProgress
  })
}

/**
 * 获取文件列表 (已废弃，请使用getFileListPaginated)
 * @returns {Promise} - 返回文件列表
 */
export function getFileList() {
  return request({
    url: '/api/files',
    method: 'get'
  })
}

/**
 * 获取分页文件列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 当前页码
 * @param {Number} params.page_size - 每页大小
 * @param {String} params.sort_field - 排序字段
 * @param {String} params.sort_order - 排序方向 (asc/desc)
 * @param {String} params.status - 状态筛选
 * @param {String} params.size_operator - 大小操作符
 * @param {String} params.size_value - 大小值
 * @param {String} params.name - 名称筛选
 * @param {String} params.start_date - 开始日期
 * @param {String} params.end_date - 结束日期
 * @returns {Promise} - 返回分页文件列表
 */
export function getFileListPaginated(params) {
  return request({
    url: '/api/files',
    method: 'get',
    params
  })
}

/**
 * 获取文件详情
 * @param {Number} fileId - 文件ID
 * @returns {Promise} - 返回文件详情
 */
export function getFileDetail(fileId) {
  return request({
    url: `/api/files/${fileId}`,
    method: 'get'
  })
}

/**
 * 删除文件
 * @param {Number} fileId - 文件ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteFile(fileId) {
  return request({
    url: `/api/files/${fileId}`,
    method: 'delete'
  })
}

/**
 * 解析文件
 * @param {Number} fileId - 文件ID
 * @returns {Promise} - 返回解析结果
 */
export function analyzeFile(fileId) {
  console.log(`开始解析文件ID: ${fileId}`)
  return request({
    url: `/api/files/${fileId}/analyze`,
    method: 'post'
  })
}

/**
 * 下载分析结果文件
 * @param {Number} fileId - 文件ID
 * @returns {String} - 返回下载链接
 */
export function downloadAnalysisResult(fileId) {
  // 返回完整的下载URL（包含baseURL）
  const downloadUrl = `${apiBaseUrl}/api/files/${fileId}/download_result`;
  console.log(`生成下载链接: ${downloadUrl} 用于文件ID: ${fileId}`);
  return downloadUrl;
}

/**
 * 获取文件的MIME类型
 * @param {String} filename - 文件名
 * @returns {String} - 返回MIME类型
 */
export function getMimeType(filename) {
  if (!filename) return 'text/plain';
  
  const extension = filename.toLowerCase().split('.').pop();
  const mimeTypes = {
    'txt': 'text/plain',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'mp3': 'audio/mpeg',
    'mp4': 'video/mp4',
    'wav': 'audio/wav',
    'json': 'application/json',
    'xml': 'application/xml',
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed'
  };
  
  return mimeTypes[extension] || 'application/octet-stream';
} 