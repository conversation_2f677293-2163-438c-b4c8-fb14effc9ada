import argon2
import datetime
from db_utils import execute_query, execute_insert, execute_update, format_datetime, format_items_datetime, get_paginated_data

# 创建Argon2密码哈希器
ph = argon2.PasswordHasher()

def register_user(username, password, email, phone=None):
    """
    注册新用户
    :param username: 用户名
    :param password: 明文密码
    :param email: 邮箱
    :param phone: 手机号（可选）
    :return: (是否成功, 用户ID或错误信息)
    """
    # 检查用户名是否已存在
    check_username_sql = "SELECT id FROM users WHERE username = %s"
    result = execute_query(check_username_sql, (username,))
    if result and len(result) > 0:
        return False, "用户名已存在"
    
    # 检查邮箱是否已存在
    check_email_sql = "SELECT id FROM users WHERE email = %s"
    result = execute_query(check_email_sql, (email,))
    if result and len(result) > 0:
        return False, "邮箱已被使用"
    
    # 如果提供了手机号，检查手机号是否已存在
    if phone:
        check_phone_sql = "SELECT id FROM users WHERE phone = %s"
        result = execute_query(check_phone_sql, (phone,))
        if result and len(result) > 0:
            return False, "手机号已被使用"
    
    # 使用Argon2算法对密码进行哈希处理
    try:
        password_hash = ph.hash(password)
    except Exception as e:
        return False, f"密码哈希失败: {str(e)}"
    
    # 插入新用户记录
    insert_sql = """
    INSERT INTO users (username, password_hash, email, phone, created_at, updated_at)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    now = datetime.datetime.now()
    params = (username, password_hash, email, phone, now, now)
    
    return execute_insert(insert_sql, params)

def login_user(username, password):
    """
    用户登录验证
    :param username: 用户名
    :param password: 明文密码
    :return: (是否成功, 用户信息或错误信息)
    """
    # 获取用户信息（支持用户名或邮箱登录）
    sql = """
    SELECT id, username, password_hash, email, phone, status, role 
    FROM users 
    WHERE username = %s OR email = %s
    """
    result = execute_query(sql, (username, username))
    
    if not result or len(result) == 0:
        return False, "用户不存在"
    
    user_info = result[0]
    
    # 检查账号是否已禁用
    if user_info['status'] == 0:
        return False, "账号已被禁用"
    
    # 验证密码
    try:
        ph.verify(user_info['password_hash'], password)
        # 密码验证成功，更新最后登录时间
        update_login_time(user_info['id'])
        
        # 转换角色
        if user_info['role'] == 'admin':
            user_info['role'] = 0
        else:
            user_info['role'] = 1
        
        # 返回用户信息（不包含密码哈希）
        del user_info['password_hash']
        return True, user_info
    except argon2.exceptions.VerifyMismatchError:
        return False, "密码错误"
    except Exception as e:
        return False, f"验证过程出错: {str(e)}"

def update_login_time(user_id):
    """
    更新用户最后登录时间
    :param user_id: 用户ID
    :return: 是否成功
    """
    sql = "UPDATE users SET last_login = %s WHERE id = %s"
    params = (datetime.datetime.now(), user_id)
    success, _ = execute_update(sql, params)
    return success

def get_user_by_id(user_id):
    """
    根据ID获取用户信息
    :param user_id: 用户ID
    :return: 用户信息
    """
    sql = """
    SELECT id, username, email, phone, created_at, updated_at, last_login, 
           status, role
    FROM users 
    WHERE id = %s
    """
    result = execute_query(sql, (user_id,))
    
    if result and len(result) > 0:
        user_data = result[0]
        # 转换角色
        if user_data['role'] == 'admin':
            user_data['role'] = 0
        else:
            user_data['role'] = 1
        # 转换日期时间格式
        format_datetime(user_data)
        return user_data
    return None

def update_user_profile(user_id, email=None, phone=None):
    """
    更新用户个人资料
    :param user_id: 用户ID
    :param email: 新邮箱（可选）
    :param phone: 新手机号（可选）
    :return: (是否成功, 成功消息或错误信息)
    """
    if not email and not phone:
        return False, "没有提供要更新的信息"
    
    # 检查用户是否存在
    check_user_sql = "SELECT id FROM users WHERE id = %s"
    result = execute_query(check_user_sql, (user_id,))
    if not result or len(result) == 0:
        return False, "用户不存在"
    
    update_fields = []
    params = []
    
    # 检查邮箱是否需要更新
    if email:
        # 检查新邮箱是否已被其他用户使用
        check_email_sql = "SELECT id FROM users WHERE email = %s AND id != %s"
        result = execute_query(check_email_sql, (email, user_id))
        if result and len(result) > 0:
            return False, "邮箱已被其他用户使用"
        
        update_fields.append("email = %s")
        params.append(email)
    
    # 检查手机号是否需要更新
    if phone:
        # 检查新手机号是否已被其他用户使用
        check_phone_sql = "SELECT id FROM users WHERE phone = %s AND id != %s"
        result = execute_query(check_phone_sql, (phone, user_id))
        if result and len(result) > 0:
            return False, "手机号已被其他用户使用"
        
        update_fields.append("phone = %s")
        params.append(phone)
    
    # 添加更新时间
    update_fields.append("updated_at = %s")
    params.append(datetime.datetime.now())
    
    # 构建更新SQL
    update_sql = f"UPDATE users SET {', '.join(update_fields)} WHERE id = %s"
    params.append(user_id)
    
    success, affected_rows = execute_update(update_sql, params)
    if success and affected_rows > 0:
        return True, "个人资料更新成功"
    else:
        return False, "个人资料更新失败"

def change_password(user_id, old_password, new_password):
    """
    修改用户密码
    :param user_id: 用户ID
    :param old_password: 旧密码
    :param new_password: 新密码
    :return: (是否成功, 成功消息或错误信息)
    """
    # 获取用户信息
    get_user_sql = "SELECT password_hash FROM users WHERE id = %s"
    result = execute_query(get_user_sql, (user_id,))
    
    if not result or len(result) == 0:
        return False, "用户不存在"
    
    user_info = result[0]
    
    # 验证旧密码
    try:
        ph.verify(user_info['password_hash'], old_password)
    except argon2.exceptions.VerifyMismatchError:
        return False, "旧密码错误"
    except Exception as e:
        return False, f"验证过程出错: {str(e)}"
    
    # 对新密码进行哈希处理
    try:
        new_password_hash = ph.hash(new_password)
    except Exception as e:
        return False, f"密码哈希失败: {str(e)}"
    
    # 更新密码
    update_sql = "UPDATE users SET password_hash = %s, updated_at = %s WHERE id = %s"
    params = (new_password_hash, datetime.datetime.now(), user_id)
    
    success, affected_rows = execute_update(update_sql, params)
    if success and affected_rows > 0:
        return True, "密码修改成功"
    else:
        return False, "密码修改失败"

# ===== 用户管理功能 =====

def get_all_users(page=1, page_size=10):
    """
    获取所有用户列表（分页）- 管理员用户优先显示
    :param page: 页码
    :param page_size: 每页记录数
    :return: 用户列表和总数
    """
    # 使用自定义SQL查询，实现管理员优先的排序
    # 计算总数
    count_sql = "SELECT COUNT(*) as total FROM users"
    count_result = execute_query(count_sql)
    total = count_result[0]['total'] if count_result else 0
    
    # 分页查询，使用CASE表达式优先排序管理员
    offset = (page - 1) * page_size
    sql = """
    SELECT id, username, email, phone, created_at, updated_at, last_login, 
           status, role
    FROM users
    ORDER BY 
        CASE 
            WHEN role = 'admin' THEN 0
            ELSE 1
        END, 
        id ASC
    LIMIT %s OFFSET %s
    """
    params = (page_size, offset)
    result = execute_query(sql, params)
    
    # 处理角色转换和日期格式化
    if result:
        for user in result:
            # 转换角色
            if user['role'] == 'admin':
                user['role'] = 0
            else:
                user['role'] = 1
                
            # 格式化日期时间
            format_datetime(user)
    
    return {
        'items': result or [],
        'total': total,
        'page': page,
        'page_size': page_size
    }

def create_user(username, password, email, phone=None, role='user', status=1):
    """
    创建新用户（管理员功能）
    :param username: 用户名
    :param password: 明文密码
    :param email: 邮箱
    :param phone: 手机号（可选）
    :param role: 用户角色（默认为普通用户）
    :param status: 账户状态（默认为启用）
    :return: (是否成功, 用户ID或错误信息)
    """
    # 检查用户名是否已存在
    check_username_sql = "SELECT id FROM users WHERE username = %s"
    result = execute_query(check_username_sql, (username,))
    if result and len(result) > 0:
        return False, "用户名已存在"
    
    # 检查邮箱是否已存在
    check_email_sql = "SELECT id FROM users WHERE email = %s"
    result = execute_query(check_email_sql, (email,))
    if result and len(result) > 0:
        return False, "邮箱已被使用"
    
    # 如果提供了手机号，检查手机号是否已存在
    if phone:
        check_phone_sql = "SELECT id FROM users WHERE phone = %s"
        result = execute_query(check_phone_sql, (phone,))
        if result and len(result) > 0:
            return False, "手机号已被使用"
    
    # 使用Argon2算法对密码进行哈希处理
    try:
        password_hash = ph.hash(password)
    except Exception as e:
        return False, f"密码哈希失败: {str(e)}"
    
    # 转换角色为数据库格式
    db_role = 'admin' if role == 0 else 'user'
    
    # 插入新用户记录
    insert_sql = """
    INSERT INTO users (username, password_hash, email, phone, role, status, created_at, updated_at)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    now = datetime.datetime.now()
    params = (username, password_hash, email, phone, db_role, status, now, now)
    
    return execute_insert(insert_sql, params)

def update_user(user_id, data):
    """
    更新用户信息（管理员功能）
    :param user_id: 用户ID
    :param data: 要更新的数据字典
    :return: (是否成功, 成功消息或错误信息)
    """
    # 检查用户是否存在
    check_user_sql = "SELECT id FROM users WHERE id = %s"
    result = execute_query(check_user_sql, (user_id,))
    if not result or len(result) == 0:
        return False, "用户不存在"
    
    update_fields = []
    params = []
    
    # 处理各字段更新
    if 'username' in data and data['username']:
        # 检查用户名是否已被其他用户使用
        check_username_sql = "SELECT id FROM users WHERE username = %s AND id != %s"
        result = execute_query(check_username_sql, (data['username'], user_id))
        if result and len(result) > 0:
            return False, "用户名已被其他用户使用"
        
        update_fields.append("username = %s")
        params.append(data['username'])
    
    if 'email' in data and data['email']:
        # 检查邮箱是否已被其他用户使用
        check_email_sql = "SELECT id FROM users WHERE email = %s AND id != %s"
        result = execute_query(check_email_sql, (data['email'], user_id))
        if result and len(result) > 0:
            return False, "邮箱已被其他用户使用"
        
        update_fields.append("email = %s")
        params.append(data['email'])
    
    if 'phone' in data:
        # 允许清空手机号
        if data['phone'] is None or data['phone'] == '':
            update_fields.append("phone = NULL")
        else:
            # 检查手机号是否已被其他用户使用
            check_phone_sql = "SELECT id FROM users WHERE phone = %s AND id != %s"
            result = execute_query(check_phone_sql, (data['phone'], user_id))
            if result and len(result) > 0:
                return False, "手机号已被其他用户使用"
            
            update_fields.append("phone = %s")
            params.append(data['phone'])
    
    if 'password' in data and data['password']:
        # 对新密码进行哈希处理
        try:
            password_hash = ph.hash(data['password'])
            update_fields.append("password_hash = %s")
            params.append(password_hash)
        except Exception as e:
            return False, f"密码哈希失败: {str(e)}"
    
    if 'status' in data:
        update_fields.append("status = %s")
        params.append(int(data['status']))
    
    if 'role' in data:
        # 转换角色为数据库格式
        db_role = 'admin' if data['role'] == 0 else 'user'
        update_fields.append("role = %s")
        params.append(db_role)
    
    # 如果没有要更新的字段
    if not update_fields:
        return False, "没有提供要更新的信息"
    
    # 添加更新时间
    update_fields.append("updated_at = %s")
    params.append(datetime.datetime.now())
    
    # 构建更新SQL
    update_sql = f"UPDATE users SET {', '.join(update_fields)} WHERE id = %s"
    params.append(user_id)
    
    success, affected_rows = execute_update(update_sql, params)
    if success and affected_rows > 0:
        return True, "用户信息更新成功"
    else:
        return False, "用户信息更新失败"

def delete_user(user_id):
    """
    删除用户（管理员功能）
    :param user_id: 用户ID
    :return: (是否成功, 成功消息或错误信息)
    """
    # 检查用户是否存在
    check_user_sql = "SELECT id, role FROM users WHERE id = %s"
    result = execute_query(check_user_sql, (user_id,))
    if not result or len(result) == 0:
        return False, "用户不存在"
    
    # 检查是否为系统唯一管理员
    if result[0]['role'] == 'admin':
        admin_count_sql = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'"
        admin_count = execute_query(admin_count_sql)[0]['count']
        if admin_count <= 1:
            return False, "不能删除系统唯一的管理员账户"
    
    # 删除用户
    delete_sql = "DELETE FROM users WHERE id = %s"
    success, affected_rows = execute_update(delete_sql, (user_id,))
    
    if success and affected_rows > 0:
        return True, "用户删除成功"
    else:
        return False, "用户删除失败"
