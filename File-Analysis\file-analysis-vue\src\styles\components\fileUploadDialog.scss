.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
  
  .drop-area {
    width: 100%;
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover, &.drag-over {
      border-color: #409eff;
      background-color: rgba(64, 158, 255, 0.06);
    }
    
    &.disabled {
      cursor: not-allowed;
      opacity: 0.7;
      
      &:hover {
        border-color: #dcdfe6;
        background-color: transparent;
      }
    }
    
    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 10px;
    }
    
    .upload-text {
      color: #606266;
      font-size: 14px;
      margin-bottom: 10px;
      
      .file-label {
        color: #409eff;
        cursor: pointer;
        
        &:hover {
          text-decoration: underline;
        }
        
        &.disabled {
          cursor: not-allowed;
          color: #c0c4cc;
          
          &:hover {
            text-decoration: none;
          }
        }
      }
    }
    
    .upload-tip {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .selected-file {
    width: 100%;
    margin-top: 20px;
    
    .file-info {
      display: flex;
      align-items: center;
      padding: 10px 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      .el-icon {
        color: #409eff;
        margin-right: 8px;
      }
      
      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .file-size {
        color: #909399;
        margin: 0 10px;
      }
      
      .remove-btn {
        color: #f56c6c;
      }
    }
  }
  
  .upload-progress {
    margin-top: 20px;
    width: 100%;
    
    .progress-text {
      margin-top: 5px;
      text-align: center;
      color: #606266;
    }
  }
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
} 