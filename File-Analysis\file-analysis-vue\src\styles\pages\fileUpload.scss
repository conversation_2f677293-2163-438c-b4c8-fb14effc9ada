.file-import-container {
  padding: 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    
    .native-upload {
      width: 100%;
      max-width: 500px;
      
      .drop-area {
        border: 2px dashed #dcdfe6;
        border-radius: 6px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover, &.drag-over {
          border-color: #409eff;
          background-color: rgba(64, 158, 255, 0.06);
        }
        
        .upload-icon {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 10px;
        }
        
        .upload-text {
          color: #606266;
          font-size: 14px;
          margin-bottom: 10px;
          
          .file-label {
            color: #409eff;
            cursor: pointer;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
        
        .upload-tip {
          color: #909399;
          font-size: 12px;
        }
      }
    }
    
    .selected-file {
      width: 100%;
      max-width: 500px;
      margin-top: 20px;
      
      .file-info {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        .el-icon {
          color: #409eff;
          margin-right: 8px;
        }
        
        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .file-size {
          color: #909399;
          margin: 0 10px;
        }
        
        .remove-btn {
          color: #f56c6c;
        }
      }
    }
    
    .upload-progress {
      margin-top: 20px;
      width: 100%;
      max-width: 500px;
      
      .progress-text {
        margin-top: 5px;
        text-align: center;
        color: #606266;
      }
    }
    
    .upload-actions {
      margin-top: 20px;
    }
  }
} 