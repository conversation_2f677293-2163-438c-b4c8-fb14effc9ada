<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'is-collapsed': isCollapsed }">
      <div class="logo-container">
        <el-icon class="logo"><Menu /></el-icon>
        <h1 v-show="!isCollapsed" class="title"></h1>
      </div>
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          unique-opened
          router
        >
          <el-menu-item index="/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <template #title>首页</template>
          </el-menu-item>
          <el-sub-menu index="/emotion">
            <template #title>
              <el-icon><Folder /></el-icon>
              <span>情绪分析</span>
            </template>
            <el-menu-item index="/emotion/analysis">
              <el-icon><List /></el-icon>
              <span>情绪分析</span>
            </el-menu-item>
            <el-menu-item index="/emotion/warning">
              <el-icon><User /></el-icon>
              <span>情绪预警</span>
            </el-menu-item>
            <el-menu-item index="/emotion/comprehensive">
              <el-icon><User /></el-icon>
              <span>综合分析</span>
            </el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="/user">
            <template #title>
              <el-icon><Folder /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/user/user">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-scrollbar>
    </div>
    
    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="header">
        <div class="left">
          <el-icon class="toggle-button" @click="toggleSidebar">
            <component :is="isCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
          <el-icon class="refresh-button" @click="refreshPage">
            <Refresh />
          </el-icon>
        </div>
        <div class="system-title">AI情绪分析系统</div>
        <div class="right">
          <el-dropdown trigger="click">
            <div class="avatar-container">
              <el-icon class="avatar"><User /></el-icon>
              <span class="username">{{ userStore.username }}</span>
              <span class="role-tag" v-if="userStore.role === 0">管理员</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="router.push('/user/profile')">个人中心</el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
    
    <!-- 文件上传对话框 -->
    <file-upload-dialog 
      v-model="showUploadDialog"
      @upload-success="handleUploadSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { HomeFilled, Refresh, List, FolderAdd, Folder, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import FileUploadDialog from '@/components/FileUploadDialog.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 侧边栏折叠状态
const isCollapsed = ref(false)
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 文件上传对话框
const showUploadDialog = ref(false)

// 处理文件上传成功
const handleUploadSuccess = () => {
  // 如果当前在文件管理页面，刷新列表
  if (route.path === '/emotion/analysis') {
    window.location.reload()
  } else {
    // 否则跳转到文件管理页面
    router.push('/emotion/analysis')
  }
}

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 处理退出登录
const handleLogout = () => {
  // 调用store中的登出方法
  userStore.logout()
  // 跳转到登录页
  router.push('/login')
  ElMessage.success('已退出登录')
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
  ElMessage.success('页面已刷新')
}
</script>

<style lang="scss">
@use '@/styles/layouts/mainLayout.scss';
</style> 