// 综合分析页面样式

.comprehensive-analysis-wrapper {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .title {
        font-size: 22px;
        font-weight: 600;
      }
    }
    
    .right {
      display: flex;
      gap: 10px;
    }
  }
  
  .statistics-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    
    .statistic-card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        .icon {
          font-size: 24px;
          margin-right: 10px;
          
          &.positive {
            color: #67c23a;
          }
          
          &.negative {
            color: #f56c6c;
          }
          
          &.neutral {
            color: #909399;
          }
          
          &.total {
            color: #409EFF;
          }
        }
        
        .title {
          font-size: 16px;
          font-weight: 500;
          color: #606266;
        }
      }
      
      .card-value {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
        
        .trend {
          font-size: 14px;
          margin-left: 10px;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
      
      .card-footer {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .chart-section {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    padding: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .title {
        font-size: 18px;
        font-weight: 500;
      }
      
      .right {
        display: flex;
        align-items: center;
        gap: 15px;
      }
    }
    
    .chart-container {
      height: 400px;
    }
    
    .chart-legend {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      gap: 30px;
      
      .legend-item {
        display: flex;
        align-items: center;
        
        .color-block {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          border-radius: 2px;
          
          &.positive {
            background-color: #67c23a;
          }
          
          &.negative {
            background-color: #f56c6c;
          }
          
          &.neutral {
            background-color: #e6a23c;
          }
        }
        
        .label {
          font-size: 14px;
        }
      }
    }
  }
  
  .analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
    
    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
    
    .analysis-card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        .title {
          font-size: 18px;
          font-weight: 500;
        }
        
        .right {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
      
      .card-content {
        .chart-container {
          height: 300px;
          margin-bottom: 20px;
        }
      }
    }
  }
  
  .table-section {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .title {
        font-size: 18px;
        font-weight: 500;
      }
      
      .right {
        display: flex;
        align-items: center;
        gap: 15px;
      }
    }
    
    .tab-container {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      
      .el-tabs__content {
        flex-grow: 1;
        padding: 20px 0;
      }
    }
    
    .table-container {
      flex-grow: 1;
    }
    
    .pagination-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
} 