-- 创建上传文件表
CREATE TABLE uploaded_files (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符（自增主键）',
    filename VARCHAR(255) NOT NULL COMMENT '原始文件名称',
    unique_filename VARCHAR(255) NOT NULL UNIQUE COMMENT '系统生成的唯一文件名称（用于避免重名冲突）',
    filesize BIGINT UNSIGNED COMMENT '文件大小（单位：字节）',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status ENUM('未解析', '解析中', '解析成功', '解析失败') DEFAULT '未解析' COMMENT '文件解析状态',
    analysis_result VARCHAR(255) COMMENT '情感分析结果文本',
    result_file VARCHAR(255) COMMENT '结果文件名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户上传文件及分析结果表';
--创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(128) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    status TINYINT DEFAULT 1 COMMENT '账户状态：1-正常，0-禁用',
    role ENUM('user','admin') DEFAULT 'user' COMMENT '用户角色'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;