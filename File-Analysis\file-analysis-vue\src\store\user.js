import { defineStore } from 'pinia'
import { login, getUserInfo, updateUserProfile, changePassword } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    userId: localStorage.getItem('userId') || null,
    username: localStorage.getItem('username') || '',
    email: localStorage.getItem('email') || '',
    phone: localStorage.getItem('phone') || '',
    role: localStorage.getItem('role') ? Number(localStorage.getItem('role')) : 1,
    isLoggedIn: !!localStorage.getItem('userId')
  }),
  
  actions: {
    // 登录
    async login(loginData) {
      try {
        const res = await login(loginData)
        if (res && res.user) {
          this.userId = res.user.id
          this.username = res.user.username
          this.email = res.user.email || ''
          this.phone = res.user.phone || ''
          this.role = res.user.role !== undefined ? Number(res.user.role) : 1
          this.isLoggedIn = true
          
          // 保存到本地存储
          localStorage.setItem('userId', res.user.id)
          localStorage.setItem('username', res.user.username)
          localStorage.setItem('email', res.user.email || '')
          localStorage.setItem('phone', res.user.phone || '')
          localStorage.setItem('role', res.user.role !== undefined ? res.user.role : 1)
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 登出
    logout() {
      this.userId = null
      this.username = ''
      this.email = ''
      this.phone = ''
      this.role = 1
      this.isLoggedIn = false
      
      // 清除本地存储
      localStorage.removeItem('userId')
      localStorage.removeItem('username')
      localStorage.removeItem('email')
      localStorage.removeItem('phone')
      localStorage.removeItem('role')
    },
    
    // 获取用户信息
    async getUserInfo() {
      if (!this.userId) return Promise.reject('未登录')
      
      try {
        const res = await getUserInfo(this.userId)
        if (res) {
          this.username = res.username
          this.email = res.email || ''
          this.phone = res.phone || ''
          this.role = res.role !== undefined ? Number(res.role) : 1
          
          // 更新本地存储
          localStorage.setItem('username', res.username)
          localStorage.setItem('email', res.email || '')
          localStorage.setItem('phone', res.phone || '')
          localStorage.setItem('role', res.role !== undefined ? res.role : 1)
          
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 更新用户资料
    async updateProfile(profileData) {
      if (!this.userId) return Promise.reject('未登录')
      
      try {
        const res = await updateUserProfile(this.userId, profileData)
        if (res) {
          // 如果更新了邮箱
          if (profileData.email) {
            this.email = profileData.email
            localStorage.setItem('email', profileData.email)
          }
          
          // 如果更新了手机号
          if (profileData.phone) {
            this.phone = profileData.phone
            localStorage.setItem('phone', profileData.phone)
          }
          
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 修改密码
    async changePassword(passwordData) {
      if (!this.userId) return Promise.reject('未登录')
      
      try {
        const res = await changePassword(this.userId, passwordData)
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    }
  }
}) 