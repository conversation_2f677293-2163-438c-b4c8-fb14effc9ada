<template>
  <div class="register-container">
    <!-- 添加电路光效元素 -->
    <div class="circuit-line line1"></div>
    <div class="circuit-line line2"></div>
    <div class="circuit-line line3"></div>
    <div class="circuit-dot dot1"></div>
    <div class="circuit-dot dot2"></div>
    <div class="circuit-dot dot3"></div>
    <div class="circuit-dot dot4"></div>
    
    <div class="register-card">
      <div class="register-title">用户注册</div>
      
      <el-form 
        ref="registerFormRef" 
        :model="registerForm" 
        :rules="registerRules" 
        label-position="top"
        class="register-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="registerForm.username" 
            placeholder="请输入用户名"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input 
            v-model="registerForm.email" 
            placeholder="请输入邮箱"
            prefix-icon="Message"
          />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input 
            v-model="registerForm.phone" 
            placeholder="请输入手机号(选填)"
            prefix-icon="Phone"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="registerForm.password" 
            type="password" 
            placeholder="请输入密码"
            prefix-icon="Lock"
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="registerForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入密码"
            prefix-icon="Lock"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading" 
            class="register-button" 
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-link">
        已有账号？<router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { register } from '@/api/user'

const router = useRouter()

// 注册表单
const registerFormRef = ref(null)
const registerForm = reactive({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

// 密码一致性验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 邮箱格式验证
const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
  if (!emailRegex.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

// 手机号格式验证（可选）
const validatePhone = (rule, value, callback) => {
  if (value && value.trim() !== '') {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(value)) {
      callback(new Error('请输入有效的手机号'))
    } else {
      callback()
    }
  }
  callback()
}

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { validator: validateEmail, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)

// 注册处理
const handleRegister = () => {
  registerFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    loading.value = true
    try {
      const res = await register({
        username: registerForm.username,
        email: registerForm.email,
        phone: registerForm.phone || null,
        password: registerForm.password
      })
      
      ElMessage.success('注册成功，请登录')
      router.push('/login')
    } catch (error) {
      console.error('注册失败:', error)
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
@use '@/styles/pages/register.scss';

/* 添加特定样式覆盖ElmentPlus默认样式 */
:deep(.register-form) {
  .el-form-item__label {
    color: #ffffff !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* 添加电路板光效元素样式 */
.circuit-line {
  position: absolute;
  background: linear-gradient(90deg, rgba(0, 255, 220, 0), rgba(0, 255, 220, 0.5), rgba(0, 255, 220, 0));
  height: 2px;
  z-index: 2;
  animation: circuit-line-animation 8s infinite;
}

.line1 {
  width: 200px;
  top: 15%;
  right: 5%;
  transform: rotate(-45deg);
}

.line2 {
  width: 300px;
  bottom: 40%;
  left: 10%;
  transform: rotate(25deg);
}

.line3 {
  width: 150px;
  top: 60%;
  right: 20%;
  transform: rotate(10deg);
}

.circuit-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: rgba(0, 255, 220, 0.8);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 10px rgba(0, 255, 220, 0.8);
  animation: circuit-dot-pulse 4s infinite alternate;
}

.dot1 {
  top: 15%;
  left: 25%;
  animation-delay: 0.5s;
}

.dot2 {
  top: 40%;
  right: 15%;
  animation-delay: 1.5s;
}

.dot3 {
  bottom: 25%;
  left: 20%;
  animation-delay: 2.5s;
}

.dot4 {
  bottom: 10%;
  right: 30%;
  animation-delay: 3.5s;
}

@keyframes circuit-line-animation {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes circuit-dot-pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 1;
  }
}
</style> 