<template>
  <div class="dashboard-container">
    <!-- 导航与用户区 (顶部固定) -->
    <el-header class="header">
      <div class="logo">
        <img src="@/assets/logo.png" alt="分析系统" class="logo-img">
        <span>FileInsight Analyzer</span>
      </div>
      <el-dropdown class="user-menu" trigger="click">
        <span class="el-dropdown-link">
          <el-icon><User /></el-icon>
          <!-- 消息通知徽标 -->
          <el-badge :value="5" class="notification-badge" :hidden="notificationCount === 0">
            <el-icon><Bell /></el-icon>
          </el-badge>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-header>

    <el-main class="main-content">
      <!-- 文件操作区 (核心功能区) -->
      <el-card class="upload-card">
        <template #header>
          <div class="clearfix">
            <span>文件上传与分析</span>
          </div>
        </template>
        <!-- 拖拽上传区域 -->
        <el-upload
          class="file-uploader"
          drag
          action="/api/upload"
          multiple
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">拖拽文件到此处或<em>点击上传</em></div>
          <div class="el-upload__tip">支持PDF/Excel/文本等文件，单个文件大小不超过10MB</div>
        </el-upload>

        <!-- 分析模式选择 -->
        <div class="analysis-cards">
          <h3>选择分析模式:</h3>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" v-for="mode in analysisModes" :key="mode.id">
              <el-card class="analysis-mode-card" :class="{ 'is-selected': selectedMode && selectedMode.id === mode.id }" @click="selectMode(mode)">
                <el-icon class="mode-icon" :size="32">
                  <component :is="mode.icon" />
                </el-icon>
                <div class="mode-name">{{ mode.name }}</div>
                <div class="mode-description">{{ mode.description }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-row :gutter="20" class="dashboard-widgets">
        <!-- 分析记录展示 (动态列表) -->
        <el-col :span="12">
          <el-card class="recent-files-card">
            <template #header>
              <div class="clearfix">
                <span>最近分析记录</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllRecords">查看全部</el-button>
              </div>
            </template>
            <el-timeline v-if="recentFiles.length > 0">
              <el-timeline-item
                v-for="item in recentFiles.slice(0, 5)"
                :key="item.id"
                :timestamp="item.time"
                placement="top"
              >
                <el-card>
                  <h4>{{ item.name }}</h4>
                  <p>
                    状态: <el-tag :type="getStatusColor(item.status)">{{ item.status }}</el-tag>
                    <el-progress v-if="item.progress !== undefined" :percentage="item.progress" :stroke-width="6" :show-text="false"></el-progress>
                  </p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无分析记录"></el-empty>
          </el-card>
        </el-col>

        <!-- 数据看板 (可视化区) -->
        <el-col :span="12">
          <el-card class="data-overview-card">
            <template #header>
              <div class="clearfix">
                <span>数据概览</span>
              </div>
            </template>
            <!-- 统计卡片 -->
            <el-row :gutter="20" class="stats-row">
              <el-col :span="8" v-for="stat in stats" :key="stat.title">
                <el-card class="stat-card">
                  <div class="stat-title">{{ stat.title }}</div>
                  <div class="stat-value">{{ stat.value }}</div>
                  <div :class="['stat-trend', stat.trendClass]">
                    <el-icon><component :is="stat.trendIcon === 'el-icon-top' ? 'CaretTop' : 'CaretBottom'" /></el-icon> {{ stat.trendText }}
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <!-- 图表集成 (ECharts) -->
            <div class="chart-container" v-loading="chartLoading" element-loading-text="加载图表数据...">
              <div id="analysisChart" style="width: 100%; height: 300px;" v-if="chartVisible"></div>
              <el-empty v-else description="图表数据加载失败或暂无数据"></el-empty>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  Bell, 
  Upload, 
  Document, 
  DataAnalysis, 
  Lock, 
  CaretTop, 
  CaretBottom 
} from '@element-plus/icons-vue'
// 引入 ECharts (如果已安装)
// import * as echarts from 'echarts' // 请确保已通过 npm install echarts 安装

const notificationCount = ref(5) // 消息通知数量
const analysisModes = ref([ // 分析模式列表
  { id: 'text', name: '文本解析', icon: 'Document', description: '提取文本内容，关键词分析' },
  { id: 'data', name: '数据可视化', icon: 'DataAnalysis', description: '表格数据分析，生成图表' },
  { id: 'security', name: '安全扫描', icon: 'Lock', description: '敏感信息检测，安全漏洞扫描' },
])
const selectedMode = ref(null) // 当前选中的分析模式
const recentFiles = ref([ // 最近分析文件列表
  { id: 1, name: '季度报告.pdf', status: '成功', time: '2023-10-26 14:30', progress: 100 },
  { id: 2, name: '用户数据.xlsx', status: '处理中', time: '2023-10-26 10:00', progress: 60 },
  { id: 3, name: '日志文件.txt', status: '失败', time: '2023-10-25 18:00', progress: 100 },
  { id: 4, name: '项目计划.docx', status: '成功', time: '2023-10-25 09:00', progress: 100 },
  { id: 5, name: '代码审查.zip', status: '排队中', time: '2023-10-24 16:00', progress: 0 },
])
const stats = ref([ // 统计数据卡片
  { title: '文件总数', value: '1,234', trendText: '+12%', trendClass: 'up', trendIcon: 'el-icon-top' },
  { title: '分析成功率', value: '92%', trendText: '+3%', trendClass: 'up', trendIcon: 'el-icon-top' },
  { title: '存储用量', value: '2.5 GB', trendText: '-5%', trendClass: 'down', trendIcon: 'el-icon-bottom' },
])
const chartOptions = ref({}) // ECharts 图表配置
const chartVisible = ref(false) // 控制图表是否显示，用于懒加载
const chartLoading = ref(true) // 控制图表加载状态

/**
 * @description 根据分析状态返回对应的 Element UI Tag 类型
 * @param {string} status - 分析状态字符串
 * @returns {string} Element UI Tag 的 type 属性值
 */
const getStatusColor = (status) => {
  switch (status) {
    case '成功':
      return 'success'
    case '失败':
      return 'danger'
    case '处理中':
      return 'warning'
    case '排队中':
      return 'info'
    default:
      return ''
  }
}

onMounted(() => {
  // 模拟数据加载和图表初始化
  initChart()
})

/**
 * @description 处理文件上传成功的回调
 * @param {Object} response - 后端返回的响应数据
 * @param {Object} file - 上传的文件对象
 * @param {Array} fileList - 当前文件列表
 */
const handleUploadSuccess = (response, file, fileList) => {
  ElMessage.success(`${file.name} 上传成功！`)
  // 可以在此处更新 recentFiles 列表
  // 例如：recentFiles.value.unshift({ id: Date.now(), name: file.name, status: '处理中', time: new Date().toLocaleString(), progress: 0 })
}

/**
 * @description 处理文件上传失败的回调
 * @param {Error} err - 错误对象
 * @param {Object} file - 上传的文件对象
 * @param {Array} fileList - 当前文件列表
 */
const handleUploadError = (err, file, fileList) => {
  ElMessage.error(`${file.name} 上传失败: ${err.message}`)
}

/**
 * @description 文件上传前的校验
 * @param {Object} file - 待上传的文件对象
 * @returns {boolean} 是否允许上传
 */
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10 // 文件大小限制 10MB
  const allowedTypes = ['application/pdf', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/plain'] // 允许的文件类型白名单
  const isAllowedType = allowedTypes.includes(file.type)

  if (!isAllowedType) {
    ElMessage.error('上传文件只能是 PDF, Excel 或 文本 格式!')
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
  }
  return isAllowedType && isLt10M
}

/**
 * @description 选择分析模式
 * @param {Object} mode - 选中的分析模式对象
 */
const selectMode = (mode) => {
  selectedMode.value = mode
  ElMessage.info(`已选择分析模式: ${mode.name}`)
  // 可以在此处触发后续分析流程
}

/**
 * @description 查看所有分析记录
 */
const viewAllRecords = () => {
  ElMessage.info('跳转到所有分析记录页面...')
  // 实际应用中会使用 Vue Router 跳转到相应页面
}

/**
 * @description 初始化 ECharts 图表
 */
const initChart = () => {
  // 模拟数据获取延迟
  setTimeout(() => {
    // 动态导入 ECharts (懒加载示例)
    import('echarts').then(echarts => {
      chartLoading.value = false
      chartVisible.value = true // 显示图表容器

      const chartDom = document.getElementById('analysisChart')
      if (chartDom) {
        const myChart = echarts.init(chartDom)
        const options = {
          title: {
            text: '周分析趋势图',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '分析任务数',
              type: 'line',
              smooth: true,
              data: [120, 132, 101, 134, 90, 230, 210]
            }
          ]
        }
        myChart.setOption(options)
        chartOptions.value = options

        // 监听窗口大小变化，自动调整图表大小
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      } else {
        console.error('ECharts 容器未找到！')
        chartVisible.value = false // 隐藏图表容器
      }
    }).catch(error => {
      console.error('ECharts 加载失败', error)
      chartLoading.value = false
      chartVisible.value = false
      ElMessage.error('图表加载失败，请稍后再试。')
    })
  }, 1500) // 模拟网络延迟
}
</script>

<style lang="scss" scoped>
/* 引入 SCSS 样式文件 */
@use "@/styles/pages/dashboard.scss";
</style>
