// 情绪分析页面样式

.file-manage-wrapper {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .title {
        font-size: 22px;
        font-weight: 600;
      }
    }
    
    .right {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-filter {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .filter-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 20px;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
    
    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      
      .el-form-item {
        margin-right: 20px;
        min-width: 200px;
      }
    }
    
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .table-container {
    flex-grow: 1;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .el-card__header {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .title {
          font-size: 16px;
          font-weight: 500;
        }
        
        .right {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
    
    .el-card__body {
      padding: 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }
    
    .table-wrapper {
      flex-grow: 1;
      overflow: auto;
      
      .el-table {
        .file-name-cell {
          display: flex;
          align-items: center;
          
          .file-icon {
            margin-right: 8px;
            font-size: 18px;
          }
          
          .el-icon-document {
            color: #909399;
          }
          
          .el-icon-folder {
            color: #E6A23C;
          }
        }
        
        .el-table__row {
          cursor: pointer;
        }
        
        .el-table__row:hover {
          background-color: #f5f7fa;
        }
        
        .status-tag {
          &.processing {
            background-color: #e6a23c;
          }
          
          &.success {
            background-color: #67c23a;
          }
          
          &.failed {
            background-color: #f56c6c;
          }
        }
        
        .action-column {
          .el-button-group {
            .el-button {
              padding: 5px 8px;
            }
          }
        }
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: flex-end;
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
  
  .file-detail-drawer {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
      
      .title {
        font-size: 18px;
        font-weight: 500;
      }
      
      .actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .detail-content {
      .info-section {
        margin-bottom: 30px;
        
        .section-title {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .info-item {
          display: flex;
          margin-bottom: 15px;
          
          .label {
            width: 100px;
            color: #606266;
          }
          
          .value {
            flex: 1;
            word-break: break-all;
          }
        }
      }
      
      .file-preview {
        margin-bottom: 30px;
        
        .preview-container {
          border: 1px solid #ebeef5;
          border-radius: 4px;
          padding: 15px;
          min-height: 200px;
          max-height: 500px;
          overflow: auto;
          
          pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
          }
          
          img {
            max-width: 100%;
          }
        }
      }
      
      .history-section {
        .history-item {
          padding: 15px;
          border-bottom: 1px solid #ebeef5;
          
          .history-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            
            .date {
              color: #909399;
              font-size: 14px;
            }
          }
          
          .history-action {
            font-weight: 500;
          }
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
}

.file-upload-container {
  .el-upload-dragger {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .el-icon {
      font-size: 40px;
      color: #409EFF;
      margin-bottom: 10px;
    }
    
    .el-upload__text {
      color: #606266;
      font-size: 14px;
      text-align: center;
      
      em {
        color: #409EFF;
        font-style: normal;
      }
    }
  }
  
  .upload-tip {
    margin-top: 15px;
    color: #909399;
    font-size: 13px;
  }
  
  .file-list {
    margin-top: 20px;
    
    .file-item {
      display: flex;
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 10px;
      align-items: center;
      
      .file-icon {
        font-size: 24px;
        margin-right: 10px;
        color: #909399;
      }
      
      .file-info {
        flex: 1;
        overflow: hidden;
        
        .file-name {
          font-weight: 500;
          margin-bottom: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .upload-progress {
        width: 120px;
        margin: 0 10px;
      }
      
      .file-actions {
        display: flex;
        gap: 5px;
      }
    }
  }
} 