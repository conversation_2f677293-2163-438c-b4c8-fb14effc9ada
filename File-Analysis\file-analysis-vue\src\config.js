// API配置
export const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

// 其他配置
export const config = {
  // API相关配置
  api: {
    baseUrl: apiBaseUrl,
    timeout: 60000
  },
  
  // 文件上传配置
  upload: {
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['.txt', '.doc', '.docx', '.pdf', '.wav', '.mp3']
  },
  
  // 分页配置
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '50', '100']
  }
};

export default config;
