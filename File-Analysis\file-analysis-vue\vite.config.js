import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 处理模板中的一些特殊情况
        }
      }
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    open: true
  },
  esbuild: {
    // 添加 JSX 支持
    jsxFactory: 'h',
    jsxFragment: 'Fragment'
  },
  // 指定public为根目录
  publicDir: 'public',
  // 指定构建输出目录
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        main: fileURLToPath(new URL('./public/index.html', import.meta.url))
      }
    }
  }
})
