import os
import tkinter as tk
from tkinter import filedialog, messagebox
from spire.doc import Document, FileFormat
from docx import Document as DocxDocument
from docx.shared import Pt
import docx.oxml.ns
import re
# 导入配置文件
from config import REPORTS_CONFIG, TEMPLATE_CONFIG
import traceback

class MarkdownConverter:
    """Markdown文档转换工具类"""
    
    def __init__(self):
        """初始化转换器"""
        self.input_file = ""
        self.output_file = ""
    
    def format_word_document(self, docx_file_path):
        """格式化Word文档的样式
        
        参数:
            docx_file_path (str): Word文档路径
        """
        try:
            from docx.shared import Pt, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
            
            # 打开生成的文档
            docx_doc = DocxDocument(docx_file_path)
            
            # 定义标题识别的正则表达式模式
            title_number_pattern = re.compile(r'^\s*(\d+)\.\s+(.+)$')  # 匹配"1. 标题"格式
            bullet_pattern = re.compile(r'^\s*[•\-\*]\s+(.+)$')  # 匹配"• 标题"或"- 标题"格式
            
            # 提前识别主标题位置，避免误判
            main_title_index = -1
            section_title_indexes = []
            
            for i, paragraph in enumerate(docx_doc.paragraphs):
                text = paragraph.text.strip()
                if not text:
                    continue
                
                # 查找主标题
                if "语音文本情感分析报告" in text:
                    main_title_index = i
                
                # 查找节标题
                if text == "分析过程" or text == "结论":
                    section_title_indexes.append(i)
            
            # 遍历所有段落进行格式化
            for i, paragraph in enumerate(docx_doc.paragraphs):
                text = paragraph.text.strip()
                if not text:
                    continue
                
                # 判断段落类型并应用相应格式
                if i == main_title_index:
                    # 主标题：黑体 二号 居中
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.font.name = '黑体'
                        run.font.size = Pt(29)  # 二号约等于29磅
                        run.font.bold = True
                        # 设置中文字体
                        run.font.name = '黑体'
                        run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '黑体')
                
                elif i in section_title_indexes:
                    # 二级标题：宋体 三号 加粗
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    for run in paragraph.runs:
                        run.font.size = Pt(16)  # 三号约等于16磅
                        run.font.bold = True
                        run.font.italic = False  # 明确确保不倾斜
                        # 设置中文字体
                        run.font.name = '宋体'
                        run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                
                # 检测数字标题格式
                elif title_number_pattern.match(text) or text == "核心结论:" or text.startswith("核心结论："):
                    # 三级标题：宋体 四号 加粗
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    for run in paragraph.runs:
                        run.font.size = Pt(14)  # 四号约等于14磅
                        run.font.bold = True
                        run.font.italic = False  # 确保不倾斜
                        # 设置中文字体
                        run.font.name = '宋体'
                        run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                
                # 检测项目符号小标题
                elif bullet_pattern.match(text) and len(text) < 50:  # 限制长度避免误判长段落
                    # 项目符号小标题：宋体 四号 加粗
                    for run in paragraph.runs:
                        run.font.size = Pt(14)  # 四号约等于14磅
                        run.font.bold = True
                        run.font.italic = False
                        # 设置中文字体
                        run.font.name = '宋体'
                        run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                
                # 为所有段落设置行距：固定值22磅
                paragraph.paragraph_format.line_spacing = Pt(22)
            
            # 处理表格文字
            for table in docx_doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                # 表格文字：10.5pt 宋体
                                run.font.size = Pt(10.5)
                                run.font.name = '宋体'
                                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
            
            # 保存文档
            docx_doc.save(docx_file_path)
            print(f"已应用自定义格式到Word文档: {docx_file_path}")
            return True
        except Exception as e:
            print(f"格式化Word文档时出错: {str(e)}")
            traceback.print_exc()
            return False
            
    def remove_evaluation_warning(self, docx_file_path):
        """移除Spire.Doc生成的评估警告
        
        参数:
            docx_file_path (str): Word文档路径
        """
        try:
            # 打开生成的文档
            docx_doc = DocxDocument(docx_file_path)
            
            # 遍历所有段落，查找并删除包含评估警告的段落
            paragraphs_to_remove = []
            for i, paragraph in enumerate(docx_doc.paragraphs):
                if "Evaluation Warning" in paragraph.text or "Spire.Doc" in paragraph.text:
                    paragraphs_to_remove.append(paragraph)
            
            # 删除段落对象
            for para in paragraphs_to_remove:
                p = para._element
                p.getparent().remove(p)
            
            # 保存清理后的文档
            docx_doc.save(docx_file_path)
            print(f"已清除Spire.Doc评估警告")
            return True
        except Exception as e:
            print(f"清除评估警告时出错: {str(e)}")
            return False
            
    def convert_with_spire(self, input_file, output_file):
        """使用Spire.Doc进行转换
        
        参数:
            input_file (str): Markdown文件路径
            output_file (str): 输出Word文件路径
        """
        try:
            # 创建文档实例
            doc = Document()
            
            # 加载Markdown文件
            doc.LoadFromFile(input_file, FileFormat.Markdown)
            
            # 保存为Word文档
            doc.SaveToFile(output_file, FileFormat.Docx)
            
            # 释放资源
            doc.Dispose()
            
            # 使用python-docx清除Spire.Doc的评估警告
            self.remove_evaluation_warning(output_file)
            
            # 应用格式设置
            self.format_word_document(output_file)
            
            return True, f"使用Spire.Doc成功将 {input_file} 转换为 {output_file}"
        except Exception as e:
            return False, f"Spire.Doc转换错误: {str(e)}"
    
    def parse_markdown_table(self, markdown_str):
        """解析Markdown表格内容
        
        参数:
            markdown_str (str): Markdown表格字符串
        返回:
            dict: 解析后的键值对
        """
        # 按行分割表格内容，过滤分隔线
        rows = [line.strip() for line in markdown_str.split('\n')
                if line.strip() and not line.startswith('|---')]

        result = {}
        for row in rows:
            # 修正单元格分割逻辑：保留空单元格并去除首尾冗余分割
            cells = [cell.strip() for cell in row.split('|')[1:-1]]
            # 两两配对生成键值对
            for i in range(0, len(cells), 2):
                if i + 1 < len(cells):
                    key = cells[i]
                    # 处理空单元格和奇数单元格末尾的情况
                    value = cells[i + 1] if cells[i + 1] else ' '
                    result[key] = value
        return result

    def parse_markdown_excel(self, text):
        """解析带有**标记的Markdown内容
        
        参数:
            text (str): Markdown文本
        返回:
            dict: 解析后的键值对
        """
        pattern = r'\*\*([^：]+)\*\*：\s*(.*)'
        result = {}

        for line in text.split('\n'):
            line = line.strip()  # 去除首尾空格
            if line.startswith('-'):
                line = line[1:].strip()  # 去除开头的短横线和空格
            match = re.match(pattern, line)
            if match:
                key = match.group(1).strip()
                value = match.group(2).strip()
                result[key] = value

        return result

    def parse_markdown_table_hx(self, text):
        """解析横向表格
        
        参数:
            text (str): Markdown表格文本
        返回:
            dict: 解析后的键值对
        """
        # 按行分割并清理空行
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # 提取表头和数据行
        header = []
        data_rows = []

        for line in lines:
            # 按竖线分割并清理空单元格
            cells = [cell.strip() for cell in line.split('|') if cell.strip()]
            # 跳过分隔线（如包含大量 "-" 的行）
            if any('-' in cell for cell in cells):
                continue
            if not header:
                header = cells  # 第一有效行作为表头
            else:
                data_rows.append(cells)  # 后续有效行作为数据

        # 将数据转换为字典
        result = {}
        if data_rows:
            for i, key in enumerate(header):
                if i < len(data_rows[0]):
                    result[key] = data_rows[0][i]
                    
        return result

    def has_double_asterisk_data(self, text):
        """检查文本是否包含**标记
        
        参数:
            text (str): 要检查的文本
        返回:
            bool: 是否包含**标记
        """
        # 匹配 **任意内容** 的正则模式
        pattern = r'\*\*.*?\*\*'
        # 若找到至少一个匹配项，返回True
        return bool(re.search(pattern, text))

    def parse_table(self, text):
        """自动解析表格类型
        
        参数:
            text (str): Markdown表格文本
        返回:
            dict: 解析结果
        """
        # 预处理：分割行并清理，排除分隔行
        lines = []
        for line in text.split('\n'):
            stripped_line = line.strip()
            if not stripped_line:
                continue
            # 排除Markdown表格的分隔行（例如：|---|---|）
            if re.match(r'^(\|?(\s*\-+\s*\|)+\s*\-+\s*\|?)|(\|?(\s*\-+\s*)+\|?)$', stripped_line):
                continue
            # 分割行，去除头尾的空单元格
            cells = re.split(r'\s*\|\s*', stripped_line)
            if cells and cells[0] == '':
                cells = cells[1:]
            if cells and cells[-1] == '':
                cells = cells[:-1]
            # 清理单元格并保留空字符串
            cleaned_cells = [cell.strip() for cell in cells]
            lines.append(cleaned_cells)

        if not lines:
            return {}

        # 判断表格类型
        is_horizontal = (
                len(lines) >= 2 and
                all(len(line) == len(lines[0]) for line in lines) and
                len(lines[0]) >= 2
        )

        is_vertical = all(len(line) % 2 == 0 for line in lines)

        if is_horizontal:
            return self.parse_markdown_table_hx(text)
        elif is_vertical:
            return self.parse_markdown_table(text)
        else:
            return self.parse_markdown_table(text)

    def fill_word_doc(self, template_path, output_path, data):
        """动态填充Word模板
        
        参数:
            template_path (str): 模板文件路径
            output_path (str): 输出文件路径
            data (dict): 需要填充的数据字典
        """
        # 加载模板文档
        doc = DocxDocument(template_path)

        # 定义键名标准化方法（兼容中英文符号和空格）
        def normalize_key(key):
            return re.sub(r'[\s\u3000：:]+', '', key).lower()

        # 预处理数据键名
        normalized_data = {normalize_key(k): v for k, v in data.items()}

        # === 处理表格内容 ===
        for table in doc.tables:
            for row in table.rows:
                # 遍历单元格，寻找标题字段
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if not cell_text:
                        continue

                    # 标准化当前单元格内容作为键
                    current_key = normalize_key(cell_text)

                    # 如果匹配到数据键
                    if current_key in normalized_data:
                        # 定位右侧单元格（假设值在标题右侧）
                        if cell_idx + 1 < len(row.cells):
                            value_cell = row.cells[cell_idx + 1]

                            # 清空原有内容但保留格式
                            if value_cell.paragraphs:
                                for para in value_cell.paragraphs:
                                    for run in para.runs:
                                        run.text = ""
                                # 添加新内容（保留原格式）
                                new_run = value_cell.paragraphs[0].add_run(str(normalized_data[current_key]))
                                # 继承字体大小（可选）
                                new_run.font.size = Pt(10.5)

        # === 处理段落中的占位符 ===
        for para in doc.paragraphs:
            # 查找形如 {{标题}} 的占位符
            matches = re.findall(r'\{\{(\s*.*?\s*)\}\}', para.text)
            for match in matches:
                raw_key = match.strip()
                norm_key = normalize_key(raw_key)
                if norm_key in normalized_data:
                    # 替换占位符并保留格式
                    para.text = para.text.replace(f'{{{{{match}}}}}', str(normalized_data[norm_key]))

        # 保存填充后的文档
        doc.save(output_path)

    def convert_md_to_word(self, md_file_path, filename_suffix='_report'):
        """
        将Markdown文件转换为Word文件并保存到报告文件夹
        
        参数:
            md_file_path (str): Markdown文件路径
            filename_suffix (str): 输出文件名后缀
            
        返回:
            tuple: (成功标志, 消息或Word文件路径)
        """
        try:
            # 确保报告目录存在
            os.makedirs(REPORTS_CONFIG['REPORTS_FOLDER'], exist_ok=True)
            
            # 创建输出文件路径
            base_name = os.path.basename(md_file_path)
            file_name_without_ext = os.path.splitext(base_name)[0]
            output_file = os.path.join(REPORTS_CONFIG['REPORTS_FOLDER'], f"{file_name_without_ext}{filename_suffix}.docx")
            
            # 转换文件
            success, message = self.convert_with_spire(md_file_path, output_file)
            
            if success:
                # 清除评估警告
                self.remove_evaluation_warning(output_file)
                # 应用格式设置
                self.format_word_document(output_file)
                return True, output_file
            else:
                return False, message
                
        except Exception as e:
            return False, f"转换过程中出错: {str(e)}"
    
    def process_md_content(self, md_content, output_filename):
        """
        处理Markdown内容并生成Word文档
        
        参数:
            md_content (str): Markdown内容
            output_filename (str): 输出文件名(不含路径和扩展名)
            
        返回:
            tuple: (成功标志, 消息或Word文件路径)
        """
        try:
            # 确保报告目录存在
            os.makedirs(REPORTS_CONFIG['REPORTS_FOLDER'], exist_ok=True)
            
            # 创建临时Markdown文件
            temp_md_path = os.path.join(REPORTS_CONFIG['REPORTS_FOLDER'], f"{output_filename}_temp.md")
            
            # 写入内容到临时文件
            with open(temp_md_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            # 创建输出文件路径
            output_file = os.path.join(REPORTS_CONFIG['REPORTS_FOLDER'], f"{output_filename}.docx")
            
            # 如果存在模板，使用模板处理
            if os.path.exists(TEMPLATE_CONFIG['ANALYSIS_TEMPLATE']):
                # 解析Markdown内容
                if self.has_double_asterisk_data(md_content):
                    data = self.parse_markdown_excel(md_content)
                else:
                    data = self.parse_table(md_content)
                
                if data:
                    self.fill_word_doc(TEMPLATE_CONFIG['ANALYSIS_TEMPLATE'], output_file, data)
                    # 清除评估警告
                    self.remove_evaluation_warning(output_file)
                    # 应用格式设置
                    self.format_word_document(output_file)
                    # 删除临时文件
                    os.remove(temp_md_path)
                    return True, output_file
            
            # 如果没有模板或解析失败，直接转换
            success, message = self.convert_with_spire(temp_md_path, output_file)
            
            # 删除临时文件
            if os.path.exists(temp_md_path):
                os.remove(temp_md_path)
            
            if success:
                # 清除评估警告
                self.remove_evaluation_warning(output_file)
                # 应用格式设置
                self.format_word_document(output_file)
                return True, output_file
            else:
                return False, message
                
        except Exception as e:
            return False, f"处理过程中出错: {str(e)}"


class ConverterApp:
    """转换应用程序GUI界面"""
    
    def __init__(self, master):
        self.master = master
        master.title("Markdown转换工具")
        master.geometry("600x400")
        
        self.converter = MarkdownConverter()
        self.input_file = ""
        self.output_file = ""
        self.template_file = ""
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI控件"""
        # 顶部标题
        tk.Label(self.master, text="Markdown文件转换工具", font=("Arial", 16)).pack(pady=10)
        
        # 选择文件框架
        file_frame = tk.LabelFrame(self.master, text="文件选择", padx=10, pady=10)
        file_frame.pack(fill="x", padx=20, pady=5)
        
        # 输入文件选择
        tk.Label(file_frame, text="输入Markdown文件:").grid(row=0, column=0, sticky="w", pady=5)
        self.input_entry = tk.Entry(file_frame, width=50)
        self.input_entry.grid(row=0, column=1, padx=5, pady=5)
        tk.Button(file_frame, text="浏览...", command=self.browse_input).grid(row=0, column=2, padx=5, pady=5)
        
        # 输出文件选择
        tk.Label(file_frame, text="输出Word文件:").grid(row=1, column=0, sticky="w", pady=5)
        self.output_entry = tk.Entry(file_frame, width=50)
        self.output_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(file_frame, text="浏览...", command=self.browse_output).grid(row=1, column=2, padx=5, pady=5)
        
        # 模板文件选择（用于填充）
        tk.Label(file_frame, text="Word模板文件(可选):").grid(row=2, column=0, sticky="w", pady=5)
        self.template_entry = tk.Entry(file_frame, width=50)
        self.template_entry.grid(row=2, column=1, padx=5, pady=5)
        tk.Button(file_frame, text="浏览...", command=self.browse_template).grid(row=2, column=2, padx=5, pady=5)
        
        # 操作选择框架
        operation_frame = tk.LabelFrame(self.master, text="操作选择", padx=10, pady=10)
        operation_frame.pack(fill="x", padx=20, pady=5)
        
        # 转换选项
        self.operation_var = tk.StringVar(value="convert")
        tk.Radiobutton(operation_frame, text="直接转换Markdown到Word", variable=self.operation_var, 
                     value="convert").grid(row=0, column=0, sticky="w", pady=5)
        tk.Radiobutton(operation_frame, text="解析Markdown表格并填充模板", variable=self.operation_var, 
                     value="fill").grid(row=1, column=0, sticky="w", pady=5)
        
        # 操作按钮框架
        button_frame = tk.Frame(self.master)
        button_frame.pack(pady=20)
        
        # 执行按钮
        tk.Button(button_frame, text="执行转换", font=("Arial", 12), bg="#4CAF50", fg="white", 
                padx=20, command=self.execute_operation).pack(side="left", padx=10)
        
        # 退出按钮
        tk.Button(button_frame, text="退出", font=("Arial", 12), bg="#f44336", fg="white", 
                padx=20, command=self.master.destroy).pack(side="left", padx=10)
        
        # 状态栏
        self.status_var = tk.StringVar(value="准备就绪")
        status_bar = tk.Label(self.master, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def browse_input(self):
        """浏览选择输入文件"""
        filename = filedialog.askopenfilename(filetypes=[("Markdown文件", "*.md"), ("所有文件", "*.*")])
        if filename:
            self.input_file = filename
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, filename)
            
            # 自动生成输出文件名
            if not self.output_entry.get():
                # 使用配置文件中的报告文件夹路径
                base_name = os.path.basename(filename)
                file_name_without_ext = os.path.splitext(base_name)[0]
                output_file = os.path.join(REPORTS_CONFIG['REPORTS_FOLDER'], f"{file_name_without_ext}.docx")
                self.output_file = output_file
                self.output_entry.delete(0, tk.END)
                self.output_entry.insert(0, output_file)
            
    def browse_output(self):
        """浏览选择输出文件"""
        initial_dir = REPORTS_CONFIG['REPORTS_FOLDER']
        os.makedirs(initial_dir, exist_ok=True)
        filename = filedialog.asksaveasfilename(defaultextension=".docx", 
                                              filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")],
                                              initialdir=initial_dir)
        if filename:
            self.output_file = filename
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, filename)
            
    def browse_template(self):
        """浏览选择模板文件"""
        # 默认使用配置中的模板
        initial_template = TEMPLATE_CONFIG['ANALYSIS_TEMPLATE']
        initial_dir = os.path.dirname(initial_template)
        
        filename = filedialog.askopenfilename(filetypes=[("Word模板", "*.docx"), ("所有文件", "*.*")],
                                            initialdir=initial_dir,
                                            initialfile=os.path.basename(initial_template))
        if filename:
            self.template_file = filename
            self.template_entry.delete(0, tk.END)
            self.template_entry.insert(0, filename)
            
    def execute_operation(self):
        """执行转换操作"""
        input_file = self.input_entry.get()
        output_file = self.output_entry.get()
        
        if not input_file or not output_file:
            messagebox.showerror("错误", "请选择输入和输出文件")
            return
        
        operation = self.operation_var.get()
        
        try:
            self.status_var.set("处理中...")
            self.master.update()
            
            if operation == "convert":
                # 直接转换
                success, message = self.converter.convert_with_spire(input_file, output_file)
                if success:
                    messagebox.showinfo("成功", message)
                else:
                    messagebox.showerror("错误", message)
            else:
                # 填充模板
                template_file = self.template_entry.get() or TEMPLATE_CONFIG['ANALYSIS_TEMPLATE']
                if not os.path.exists(template_file):
                    messagebox.showerror("错误", "请选择有效的Word模板文件")
                    return
                
                # 读取Markdown文件
                with open(input_file, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()
                
                # 解析Markdown内容
                if self.converter.has_double_asterisk_data(markdown_content):
                    data = self.converter.parse_markdown_excel(markdown_content)
                else:
                    data = self.converter.parse_table(markdown_content)
                
                if not data:
                    messagebox.showerror("错误", "未能从Markdown中解析出有效数据")
                    return
                
                # 执行填充
                self.converter.fill_word_doc(template_file, output_file, data)
                messagebox.showinfo("成功", f"已成功将解析的数据填充到模板中并保存为: {output_file}")
                
            self.status_var.set("处理完成")
        except Exception as e:
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")


# 提供函数以便外部调用
def convert_md_to_word(md_file_path, filename_suffix='_report'):
    """
    将Markdown文件转换为Word文件并保存到报告文件夹
    
    参数:
        md_file_path (str): Markdown文件路径
        filename_suffix (str): 输出文件名后缀
        
    返回:
        str: 生成的Word文件路径，如果失败则返回错误消息
    """
    converter = MarkdownConverter()
    success, result = converter.convert_md_to_word(md_file_path, filename_suffix)
    if success:
        # 清除评估警告
        converter.remove_evaluation_warning(result)
        # 应用格式设置
        converter.format_word_document(result)
    return result if success else f"转换失败: {result}"

def convert_md_content_to_word(md_content, output_filename):
    """
    将Markdown内容转换为Word文件并保存到报告文件夹
    
    参数:
        md_content (str): Markdown内容
        output_filename (str): 输出文件名(不含路径和扩展名)
        
    返回:
        str: 生成的Word文件路径，如果失败则返回错误消息
    """
    converter = MarkdownConverter()
    success, result = converter.process_md_content(md_content, output_filename)
    if success and not isinstance(result, str):
        # 确保结果是文件路径而不是错误消息
        converter.remove_evaluation_warning(result)
        # 应用格式设置
        converter.format_word_document(result)
    return result if success else f"转换失败: {result}"

# 主程序入口
if __name__ == "__main__":
    root = tk.Tk()
    app = ConverterApp(root)
    root.mainloop() 