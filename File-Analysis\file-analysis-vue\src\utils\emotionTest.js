// 情感检测测试工具
// 用于验证前端情感检测逻辑与后端保持一致

// 测试数据 - 模拟后端返回的分析结果
const testAnalysisResults = [
  {
    id: 1,
    content: "经分析，当前主体情感具有（绝望、自暴自弃、报复心理）的心理特征",
    expectedEmotions: ['绝望', '自暴自弃', '报复心理'],
    expectedRiskLevel: '极高风险'
  },
  {
    id: 2,
    content: "经分析，当前主体情感具有（抑郁、焦虑、愤怒）的心理特征",
    expectedEmotions: ['抑郁', '焦虑', '愤怒'],
    expectedRiskLevel: '高风险'
  },
  {
    id: 3,
    content: "经分析，当前主体情感具有（焦虑、自卑、嫉妒）的心理特征",
    expectedEmotions: ['焦虑', '自卑', '嫉妒'],
    expectedRiskLevel: '中风险'
  },
  {
    id: 4,
    content: "经分析，当前主体情感具有（消极、懒散、后悔）的心理特征",
    expectedEmotions: ['消极', '懒散', '后悔'],
    expectedRiskLevel: '低风险'
  },
  {
    id: 5,
    content: "分析结果中包含绝望和抑郁等关键词，但没有标准格式",
    expectedEmotions: ['绝望', '抑郁'],
    expectedRiskLevel: '极高风险'
  }
];

// 情感分类定义（与前端保持一致）
const criticalRiskEmotions = [
  '绝望', '自暴自弃', '报复心理', '报复', '强迫观念', '麻木', '成瘾性依赖'
];

const highRiskEmotions = [
  '抑郁', '恐慌', '愤怒', '敌视', '偏执', '逃避现实', '羞耻', '执念', '精神内耗'
];

const mediumRiskEmotions = [
  '焦虑', '恐惧', '悲观', '疑病', '自满', '自负', '自卑', '孤独', '不合群',
  '猜忌', '嫉妒', '空虚', '压抑', '逆反', '易怒', '冷漠', '贪婪', '虚荣'
];

const lowRiskEmotions = [
  '消极', '依赖', '从众心理', '敏感', '逆来顺受', '懒散', '后悔', '愧疚'
];

const dangerousEmotions = [
  ...criticalRiskEmotions,
  ...highRiskEmotions,
  ...mediumRiskEmotions,
  ...lowRiskEmotions
];

// 检测危险情感函数（与前端保持一致）
function detectDangerousEmotions(content) {
  if (!content) return [];
  
  let emotions = [];
  
  // 首先尝试使用正则表达式提取情感词汇
  const pattern = /经分析，当前主体情感具有[（(]([^）)]+)[）)]/;
  const match = content.match(pattern);
  
  if (match) {
    // 提取括号中的情感词汇，分成列表
    const emotionsStr = match[1];
    emotions = emotionsStr.split(/[,，、]/).map(e => e.trim()).filter(e => e);
    
    // 只返回危险情感
    emotions = emotions.filter(emotion => dangerousEmotions.includes(emotion));
  }
  
  // 如果没有通过正则表达式匹配到，则直接搜索文本中的危险情感关键词
  if (emotions.length === 0) {
    emotions = dangerousEmotions.filter(emotion => content.includes(emotion));
  }
  
  // 去重并返回
  return [...new Set(emotions)];
}

// 获取风险等级函数（与前端保持一致）
function getRiskLevel(emotions) {
  // 检查是否包含极高风险情感
  const hasCriticalRisk = emotions.some(emotion => criticalRiskEmotions.includes(emotion));
  if (hasCriticalRisk) return '极高风险';
  
  // 检查是否包含高风险情感
  const hasHighRisk = emotions.some(emotion => highRiskEmotions.includes(emotion));
  if (hasHighRisk) return '高风险';
  
  // 检查是否包含中风险情感
  const hasMediumRisk = emotions.some(emotion => mediumRiskEmotions.includes(emotion));
  if (hasMediumRisk) return '中风险';
  
  // 其余为低风险
  return '低风险';
}

// 运行测试
function runEmotionDetectionTest() {
  console.log('=== 情感检测逻辑测试 ===');
  
  let passedTests = 0;
  let totalTests = testAnalysisResults.length;
  
  testAnalysisResults.forEach(test => {
    console.log(`\n测试 ${test.id}:`);
    console.log(`输入: ${test.content}`);
    
    const detectedEmotions = detectDangerousEmotions(test.content);
    const riskLevel = getRiskLevel(detectedEmotions);
    
    console.log(`检测到的情感: [${detectedEmotions.join(', ')}]`);
    console.log(`风险等级: ${riskLevel}`);
    console.log(`期望情感: [${test.expectedEmotions.join(', ')}]`);
    console.log(`期望风险等级: ${test.expectedRiskLevel}`);
    
    // 检查情感检测是否正确
    const emotionsMatch = test.expectedEmotions.every(emotion => 
      detectedEmotions.includes(emotion)
    ) && detectedEmotions.every(emotion => 
      test.expectedEmotions.includes(emotion)
    );
    
    // 检查风险等级是否正确
    const riskLevelMatch = riskLevel === test.expectedRiskLevel;
    
    if (emotionsMatch && riskLevelMatch) {
      console.log('✅ 测试通过');
      passedTests++;
    } else {
      console.log('❌ 测试失败');
      if (!emotionsMatch) console.log('  - 情感检测不匹配');
      if (!riskLevelMatch) console.log('  - 风险等级不匹配');
    }
  });
  
  console.log(`\n=== 测试结果 ===`);
  console.log(`通过: ${passedTests}/${totalTests}`);
  console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  return passedTests === totalTests;
}

// 导出测试函数
export { runEmotionDetectionTest, detectDangerousEmotions, getRiskLevel };
