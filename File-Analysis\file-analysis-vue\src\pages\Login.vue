<template>
  <div class="login-container">
    <!-- 添加电路光效元素 -->
    <div class="circuit-line line1"></div>
    <div class="circuit-line line2"></div>
    <div class="circuit-dot dot1"></div>
    <div class="circuit-dot dot2"></div>
    <div class="circuit-dot dot3"></div>
    
    <!-- 添加系统标题 -->
    <div class="system-title">AI情绪分析系统</div>
    
    <div class="login-card">
      <div class="login-title">系统登录</div>
      
      <el-form 
        ref="loginFormRef" 
        :model="loginForm" 
        :rules="loginRules" 
        label-position="top"
        class="login-form"
      >
        <el-form-item label="用户名/邮箱" prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名或邮箱"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            prefix-icon="Lock"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading" 
            class="login-button" 
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="register-link">
        还没有账号？<router-link to="/register">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 登录表单
const loginFormRef = ref(null)
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)

// 登录处理
const handleLogin = () => {
  loginFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    loading.value = true
    try {
      await userStore.login(loginForm)
      ElMessage.success('登录成功')
      
      // 登录成功后跳转，优先跳转到之前尝试访问的页面
      const redirectPath = route.query.redirect || '/dashboard'
      router.push(redirectPath)
    } catch (error) {
      console.error('登录失败:', error)
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
@use '@/styles/pages/login.scss';

/* 添加特定样式覆盖ElmentPlus默认样式 */
:deep(.login-form) {
  .el-form-item__label {
    color: #ffffff !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* 系统标题样式 */
.system-title {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(157, 72, 255, 0.8), 0 0 20px rgba(255, 0, 191, 0.5);
  z-index: 10;
  letter-spacing: 2px;
  text-align: center;
  animation: title-glow 3s infinite alternate;
}

@keyframes title-glow {
  0% {
    text-shadow: 0 0 10px rgba(157, 72, 255, 0.8), 0 0 20px rgba(255, 0, 191, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba(157, 72, 255, 1), 0 0 30px rgba(255, 0, 191, 0.8), 0 0 40px rgba(0, 255, 255, 0.4);
  }
}

/* 添加电路板光效元素样式 */
.circuit-line {
  position: absolute;
  background: linear-gradient(90deg, rgba(157, 72, 255, 0), rgba(157, 72, 255, 0.7), rgba(157, 72, 255, 0));
  height: 2px;
  z-index: 2;
  animation: circuit-line-animation 8s infinite;
}

.line1 {
  width: 200px;
  top: 25%;
  left: 0;
  transform: rotate(30deg);
}

.line2 {
  width: 300px;
  bottom: 30%;
  right: 10%;
  transform: rotate(-20deg);
}

.circuit-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: rgba(255, 0, 191, 0.8);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 10px rgba(255, 0, 191, 0.8);
  animation: circuit-dot-pulse 4s infinite alternate;
}

.dot1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.dot2 {
  top: 60%;
  right: 20%;
  animation-delay: 1s;
}

.dot3 {
  bottom: 15%;
  left: 30%;
  animation-delay: 2s;
}

@keyframes circuit-line-animation {
  0% {
    background-position: -200px 0;
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200px 0;
    opacity: 0.5;
  }
}

@keyframes circuit-dot-pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 1;
  }
}
</style> 